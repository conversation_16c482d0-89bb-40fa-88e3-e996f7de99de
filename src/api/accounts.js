import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 财务账户 API 服务 - RESTful风格
 */
export const accountsApi = {
  /**
   * 获取财务账户列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {Number} [params.ownerOrgId] - 机构ID
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回财务账户列表的 Promise
   */
  getList(params) {
    return doGet('/financial/accounts/setting', params)
  },

  /**
   * 获取财务账户详情
   * @param {Number} id - 财务账户ID
   * @returns {Promise} 返回财务账户详情的 Promise
   */
  getDetail(id) {
    return doGet(`/financial/accounts/setting/${id}`)
  },

  /**
   * 新增财务账户
   * @param {Object} data - 财务账户数据
   * @param {String} data.abbr - 账户名称
   * @param {Number} data.ownerOrgId - 所属机构ID
   * @param {Boolean} data.payable - 是否可付款
   * @param {Boolean} data.receivable - 是否可收款
   * @param {Boolean} data.usable - 账户状态（true可用，false停用）
   * @param {Number} data.initAmount - 期初金额（分）
   * @param {String} [data.remark] - 备注
   * @returns {Promise} 返回新增结果的 Promise
   */
  create(data) {
    return doPost('/financial/accounts/setting', data)
  },

  /**
   * 更新财务账户
   * @param {Object} data - 财务账户数据
   * @param {Number} data.id - 财务账户ID
   * @param {String} [data.abbr] - 账户名称
   * @param {Number} [data.ownerOrgId] - 所属机构ID
   * @param {Boolean} [data.payable] - 是否可付款
   * @param {Boolean} [data.receivable] - 是否可收款
   * @param {Boolean} [data.usable] - 账户状态（true可用，false停用）
   * @param {Number} [data.initAmount] - 期初金额（分）
   * @param {String} [data.remark] - 备注
   * @returns {Promise} 返回更新结果的 Promise
   */
  update(data) {
    return doPut('/financial/accounts/setting', data)
  },

  /**
   * 删除财务账户
   * @param {Number} id - 财务账户ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  delete(id) {
    return doDelete(`/financial/accounts/setting/${id}`)
  },

  /**
   * 模拟数据 - 用于开发环境测试
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回模拟数据的 Promise
   */
  getMockAccounts(params) {
    // 模拟数据
    const mockData = [
      {
        id: 1,
        abbr: '北京总部基本账户',
        accountNo: '****************',
        orgId: 1,
        orgName: '北京总部',
        bankName: '中国工商银行',
        bankBranch: '北京市朝阳支行',
        payable: true,
        receivable: true,
        usable: true, // 账户状态：可用
        initialAmount: 1000000, // 10000元（单位：分）
        createTime: '2023-01-15 10:30:00',
        updateTime: '2023-01-15 10:30:00',
        remark: '公司主要结算账户',
        creatorName: '张三'
      },
      {
        id: 2,
        abbr: '上海分公司基本账户',
        accountNo: '****************',
        orgId: 2,
        orgName: '上海分公司',
        bankName: '中国建设银行',
        bankBranch: '上海市浦东支行',
        payable: true,
        receivable: true,
        usable: true, // 账户状态：可用
        initialAmount: 500000, // 5000元（单位：分）
        createTime: '2023-01-16 14:20:00',
        updateTime: '2023-01-16 14:20:00',
        remark: '上海分公司主要结算账户',
        creatorName: '李四'
      },
      {
        id: 3,
        abbr: '广州分公司基本账户',
        accountNo: '****************',
        orgId: 3,
        orgName: '广州分公司',
        bankName: '中国银行',
        bankBranch: '广州市天河支行',
        payable: true,
        receivable: true,
        usable: false, // 账户状态：停用
        initialAmount: -200000, // -2000元（单位：分，负数）
        createTime: '2023-01-17 09:15:00',
        updateTime: '2023-01-17 09:15:00',
        remark: '广州分公司主要结算账户',
        creatorName: '王五'
      }
    ]

    // 过滤数据
    let filteredData = [...mockData]

    // 按机构ID过滤
    if (params.ownerOrgId) {
      filteredData = filteredData.filter(item => item.ownerOrgId === parseInt(params.ownerOrgId))
    }

    // 按关键词搜索
    if (params.keywords) {
      const keyword = params.keywords.toLowerCase()
      filteredData = filteredData.filter(item =>
        item.abbr.toLowerCase().includes(keyword) ||
        (item.accountNo && item.accountNo.includes(keyword)) ||
        item.orgName.toLowerCase().includes(keyword) ||
        (item.bankName && item.bankName.toLowerCase().includes(keyword)) ||
        (item.remark && item.remark.toLowerCase().includes(keyword)) ||
        (item.creatorName && item.creatorName.toLowerCase().includes(keyword))
      )
    }

    // 分页处理
    const page = params.page || 1
    const size = params.size || 20
    const start = (page - 1) * size
    const end = start + size
    const paginatedData = filteredData.slice(start, end)

    // 返回模拟响应
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        list: paginatedData,
        total: filteredData.length
      }
    })
  }
}

export default accountsApi
