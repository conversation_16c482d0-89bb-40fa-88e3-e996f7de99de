import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 赠品库存管理 API 服务 - RESTful风格
 */
export const giftStockApi = {
  /**
   * 获取赠品库存列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {Number} [params.stockOrgId] - 库存机构ID
   * @param {String} [params.category] - 赠品类型
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回赠品库存列表的 Promise
   */
  getGiftStockList(params) {
    return doGet('/gift/stock', params)
  },

  /**
   * 获取赠品库存详情
   * @param {Number} id - 赠品库存ID
   * @returns {Promise} 返回赠品库存详情的 Promise
   */
  getGiftStockDetail(id) {
    return doGet(`/gift/stock/${id}`)
  },

  /**
   * 创建赠品库存
   * @param {Object} data - 赠品库存数据
   * @param {String} data.name - 赠品名称
   * @param {String} data.category - 赠品类型
   * @param {String} data.spec - 规格型号
   * @param {String} data.unit - 单位
   * @param {Number} data.price - 单价（分）
   * @param {Number} data.quantity - 库存数量
   * @param {Number} data.stockOrgId - 库存机构ID
   * @returns {Promise} 返回创建结果的 Promise
   */
  createGiftStock(data) {
    return doPost('/gift/stock', data)
  },

  /**
   * 更新赠品库存
   * @param {Object} data - 赠品库存数据
   * @param {Number} data.id - 赠品库存ID
   * @param {String} [data.name] - 赠品名称
   * @param {String} [data.category] - 赠品类型
   * @param {String} [data.spec] - 规格型号
   * @param {String} [data.unit] - 单位
   * @param {Number} [data.price] - 单价（分）
   * @param {Number} [data.quantity] - 库存数量
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateGiftStock(data) {
    return doPut('/gift/stock', data)
  },

  /**
   * 删除赠品库存
   * @param {Number} id - 赠品库存ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteGiftStock(id) {
    return doDelete(`/gift/stock/${id}`)
  }
}

export default giftStockApi
