import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 费用科目 API 服务 - RESTful风格
 */
export const feeSubjectApi = {
  /**
   * 获取费用科目列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回费用科目列表的 Promise
   */
  getFeeSubjectList(params) {
    return doGet('/financial/fee/subject/setting', params)
  },

  /**
   * 获取费用科目详情
   * @param {Number} id - 费用科目ID
   * @returns {Promise} 返回费用科目详情的 Promise
   */
  getFeeSubjectDetail(id) {
    return doGet(`/financial/fee/subject/setting/${id}`)
  },

  /**
   * 新增费用科目
   * @param {Object} data - 费用科目数据
   * @param {String} data.subjectName - 科目名称
   * @param {String} data.subjectCategory - 科目类别
   * @param {Boolean} data.receivable - 是否可收款
   * @param {Boolean} data.payable - 是否可付款
   * @param {Boolean} data.usable - 科目状态（true启用，false停用）
   * @param {Number} data.defaultAmount - 默认金额（分）
   * @param {String} [data.remark] - 备注
   * @returns {Promise} 返回新增结果的 Promise
   */
  createFeeSubject(data) {
    return doPost('/financial/fee/subject/setting', data)
  },

  /**
   * 更新费用科目
   * @param {Object} data - 费用科目数据
   * @param {Number} data.id - 费用科目ID
   * @param {String} [data.subjectName] - 科目名称
   * @param {String} [data.subjectCategory] - 科目类别
   * @param {Boolean} [data.receivable] - 是否可收款
   * @param {Boolean} [data.payable] - 是否可付款
   * @param {Boolean} [data.usable] - 科目状态（true启用，false停用）
   * @param {Number} [data.defaultAmount] - 默认金额（分）
   * @param {String} [data.remark] - 备注
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateFeeSubject(data) {
    return doPut('/financial/fee/subject/setting', data)
  },

  /**
   * 删除费用科目
   * @param {Number} id - 费用科目ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteFeeSubject(id) {
    return doDelete(`/financial/fee/subject/setting/${id}`)
  },

  /**
   * 获取科目类别选项
   * @returns {Array} 返回科目类别选项数组
   */
  getSubjectCategoryOptions() {
    return [
      { label: '企业运营', value: 'OPENING_FEE' },
      { label: '销售关联费用', value: 'DEAL_FEE' },
      { label: '其他费用', value: 'OTHER_FEE' }
    ]
  }
}

export default feeSubjectApi
