import axios from 'axios'
import message from '@/utils/messages'
import { createDiscrete<PERSON>pi } from 'naive-ui'
import eventBus from '@/utils/eventBus'

const { loadingBar } = createDiscreteApi(['loadingBar'])

const instance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
})

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
	loadingBar.start() // 开始加载进度条
	const token = localStorage.getItem('access_token')
	if (token) {
	  config.headers['Authorization'] = `${token}`
	}
	return config
  },
  (error) => {
	loadingBar.error() // 加载出错
	message.error('请求配置错误')
	return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
	loadingBar.finish() // 完成加载进度条
	if (response.data.code !== 200) {
	  message.error(response.data.message || '请求失败')
	  return Promise.reject(new Error(response.data.message || '请求失败'))
	}
	return response.data
  },
  (error) => {
	loadingBar.error() // 加载出错
	if (axios.isCancel(error)) {
	  message.info('请求已取消')
	} else if (error.response) {
	  switch (error.response.status) {
		case 400:
			message.error(error.response.data.message || '输入有误，请检查输入信息')
			break
		case 401:
		  message.error(error.response.data.message || '授权已过期，请重新登录')
		  localStorage.removeItem('access_token')
		  eventBus.emit('auth:logout') // 触发登出事件
		  break
		case 403:
		  message.error('拒绝访问')
		  break
		case 404:
		  message.error('请求的资源不存在')
		  break
		case 500:
		  message.error('服务器内部错误')
		  break
		default:
		  message.error(`连接错误 ${error.response.status}`)
	  }
	} else if (error.request) {
	  message.error('网络错误，请检查您的网络连接')
	} else {
	  message.error('发生未知错误，请稍后重试')
	}
	return Promise.reject(error)
  }
)

export const doGet = (url, params) => instance.get(url, { params })
export const doPost = (url, data) => instance.post(url, data)
export const doPut = (url, data) => instance.put(url, data)
export const doDelete = (url) => instance.delete(url)

export default instance
