/**
 * 订单表单工具函数
 */
import { convertNumberToChinese } from '@/utils/money'

/**
 * 处理日期转换为时间戳
 * @param {Date|Number|String} date - 日期对象、时间戳或日期字符串
 * @returns {Number|null} 时间戳或null
 */
export const convertDateToTimestamp = (date) => {
  if (!date) return null
  if (typeof date === 'number') return date
  if (date instanceof Date) return date.getTime()
  return null
}

/**
 * 生成订单编号
 * @returns {String} 订单编号
 */
export const generateOrderNo = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `SO${year}${month}${day}${random}`
}

/**
 * 计算成交总价
 * @param {Object} form - 表单数据
 * @returns {Object} 更新后的表单数据
 */
export const calculateFinalPrice = (form) => {
  // 基础成交价 = 销售价格 - 优惠金额
  let basePrice = Math.max(0, form.vehicleSalePrice - (form.discountAmount || 0))

  // 如果有专享优惠且类型不是"无"，减去专享优惠金额
  if (form.exclusiveDiscountType && form.exclusiveDiscountType !== 'NONE') {
    basePrice = Math.max(0, basePrice - (form.exclusiveDiscountAmount || 0))
  }

  // 如果是分期付款，加上分期服务费
  if (form.paymentMethod === 'LOAN') {
    basePrice += (form.loanFee || 0)
  }

  form.finalPrice = basePrice
  // 更新大写金额
  form.finalPriceChinese = convertNumberToChinese(form.finalPrice)

  return form
}

/**
 * 计算预估毛利率和毛利润
 * @param {Object} form - 表单数据
 * @returns {Object} 更新后的表单数据
 */
export const calculateProfitRate = (form) => {
  // 计算总成本 = 启票价格 + 销售费用
  const totalCost = form.vehicleSbPrice + (form.salesExpense || 0)

  // 计算预估毛利润 = 成交总价 - 总成本
  form.profitAmount = form.finalPrice - totalCost

  if (totalCost > 0) {
    // 毛利率 = 毛利润 / 总成本 * 100%
    let rate = (form.profitAmount / totalCost) * 100
    // 如果毛利率为负，则显示为0
    rate = rate < 0 ? 0 : rate
    // 保留2位小数
    form.profitRate = parseFloat(rate.toFixed(2))
  } else {
    form.profitRate = 0
  }

  return form
}

/**
 * 处理贷款金额变化
 * @param {Object} form - 表单数据
 * @returns {Object} 更新后的表单数据
 */
export const handleLoanAmountChange = (form) => {
  if (form.finalPrice > 0) {
    // 当贷款金额变化时，自动计算首付金额 = 成交总价 - 贷款金额
    form.loanInitialAmount = parseFloat((form.finalPrice - form.loanAmount).toFixed(2))
    // 确保首付金额不小于0
    if (form.loanInitialAmount < 0) {
      form.loanInitialAmount = 0
      // 如果首付金额被调整为0，则贷款金额等于成交总价
      form.loanAmount = form.finalPrice
    }
    // 计算首付比例 = 首付金额 / 成交总价 * 100%
    form.loanInitialRatio = parseFloat(((form.loanInitialAmount / form.finalPrice) * 100).toFixed(2))
  }
  return form
}

/**
 * 处理首付金额变化
 * @param {Object} form - 表单数据
 * @returns {Object} 更新后的表单数据
 */
export const handleLoanInitialAmountChange = (form) => {
  if (form.finalPrice > 0) {
    // 当首付金额变化时，自动计算贷款金额 = 成交总价 - 首付金额
    form.loanAmount = parseFloat((form.finalPrice - form.loanInitialAmount).toFixed(2))
    // 确保贷款金额不小于0
    if (form.loanAmount < 0) {
      form.loanAmount = 0
      // 如果贷款金额被调整为0，则首付金额等于成交总价
      form.loanInitialAmount = form.finalPrice
    }
    // 计算首付比例 = 首付金额 / 成交总价 * 100%
    form.loanInitialRatio = parseFloat(((form.loanInitialAmount / form.finalPrice) * 100).toFixed(2))
  }
  return form
}

/**
 * 将表单数据转换为API请求数据
 * @param {Object} form - 表单数据
 * @returns {Object} API请求数据
 */
export const convertFormToApiData = (form) => {
  // 构建符合API的请求数据（使用驼峰命名法）
  const orderPayload = {
    // 客户信息
    customerId: form.customerId,
    salesOrgId: form.invoiceOrgId || form.outboundOrgId, // 销售单位ID
    salesAgentId: form.salesAgentId || form.customerId, // 销售顾问ID，如果没有则使用客户ID
    salesLeaderId: form.salesLeaderId || form.customerId, // 销售主管ID，如果没有则使用客户ID

    // SKU信息
    skuId: form.skuId,

    // 订单日期
    dealDate: form.dealDate, // 订单日期

    // 交付信息
    deliveryDate: form.expectedOutboundDate, // 交付日期
    deliveryOrgId: form.outboundOrgId, // 交付单位ID

    // 金额信息（转换为分）
    sbAmount: Math.round(form.vehicleSbPrice * 100), // 启票价格（分）
    salesAmount: Math.round(form.vehicleSalePrice * 100), // 销售价格（分）
    depositAmount: Math.round(form.depositAmount * 100), // 已付定金（分）
    depositDeductible: form.depositDeductible, // 定金是否转车款
    discountAmount: Math.round(form.discountAmount * 100), // 优惠金额（分）
    exclusiveDiscountType: form.exclusiveDiscountType || 'NONE', // 专享优惠类型
    exclusiveDiscountAmount: Math.round(form.exclusiveDiscountAmount * 100), // 专享优惠金额（分）
    salesCostAmount: Math.round(form.salesExpense * 100), // 销售费用（分）
    dealAmount: Math.round(form.finalPrice * 100), // 成交总价（分）
    dealAmountCn: form.finalPriceChinese, // 成交总价大写
    grossProfitAmount: Math.round(form.profitAmount * 100), // 毛利润（分）
    grossProfitRate: form.profitRate, // 毛利率

    // 付款方式
    paymentMethod: form.paymentMethod || 'FULL', // 默认全款

    // 备注
    remark: form.paymentRemark
  }

  // 如果选择了"有"车辆置换且填写了车牌号，添加二手车信息到请求数据
  if (form.hasUsedVehicle === 'YES' && form.usedVehicleId) {
    orderPayload.usedVehicleId = form.usedVehicleId
    orderPayload.usedVehicleVin = form.usedVehicleVin
    orderPayload.usedVehicleAmount = Math.round(form.usedVehicleAmount * 100)
    orderPayload.usedVehicleDeductible = form.usedVehicleDeductible
    orderPayload.usedVehicleDiscountAmount = form.usedVehicleDiscountAmount ? Math.round(form.usedVehicleDiscountAmount * 100) : 0
    orderPayload.usedVehicleDiscountDeductible = form.usedVehicleDiscountDeductible
    orderPayload.usedVehicleBound = form.usedVehicleBound
    orderPayload.usedVehicleModel = form.usedVehicleModel
    orderPayload.usedVehicleColor = form.usedVehicleColor
  }

  // 如果是贷款方式，添加贷款信息
  if (orderPayload.paymentMethod === 'LOAN') {
    orderPayload.loanChannel = form.loanChannel
    orderPayload.loanAmount = form.loanAmount ? Math.round(form.loanAmount * 100) : 0
    orderPayload.loanInitialAmount = form.loanInitialAmount ? Math.round(form.loanInitialAmount * 100) : 0
    orderPayload.loanMonths = form.loanMonths
    orderPayload.loanRebateAmount = form.loanRebateAmount ? Math.round(form.loanRebateAmount * 100) : 0
    orderPayload.loanRebateDeductible = form.loanRebateDeductible
    orderPayload.loanFee = form.loanFee ? Math.round(form.loanFee * 100) : 0
  }

  // 添加保险选项信息
  orderPayload.hasInsurance = form.hasInsurance

  // 添加其他衍生收入信息
  orderPayload.hasDerivativeIncome = form.hasDerivativeIncome
  if (form.hasDerivativeIncome === 'YES') {
    // 将金额从元转换为分
    orderPayload.notaryFee = Math.round(form.notaryFee * 100)
    orderPayload.carefreeIncome = Math.round(form.carefreeIncome * 100)
    orderPayload.extendedWarrantyIncome = Math.round(form.extendedWarrantyIncome * 100)
    orderPayload.vpsIncome = Math.round(form.vpsIncome * 100)
    orderPayload.preInterest = Math.round(form.preInterest * 100)
    orderPayload.licensePlateFee = Math.round(form.licensePlateFee * 100)
    orderPayload.tempPlateFee = Math.round(form.tempPlateFee * 100)
    orderPayload.deliveryEquipment = Math.round(form.deliveryEquipment * 100)
  }

  // 添加赠品明细信息
  orderPayload.hasGiftItems = form.hasGiftItems
  if (form.hasGiftItems === 'YES' && Array.isArray(form.giftItems) && form.giftItems.length > 0) {
    // 处理赠品明细数据，将单价从元转换为分
    orderPayload.giftItems = form.giftItems.map(item => ({
      ...item,
      unitPrice: Math.round((item.unitPrice || 0) * 100) // 将单价从元转换为分
    }))
  }

  return orderPayload
}
