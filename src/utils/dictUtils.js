import { dictOptions } from '@/mock/dictData'

/**
 * 字典数据工具函数
 */

/**
 * 获取字典选项列表
 * @param {string} dictCode - 字典编码
 * @param {boolean} includeAll - 是否包含"不限"选项
 * @returns {Array} 选项列表
 */
export function getDictOptions(dictCode, includeAll = true) {
  const options = includeAll ? [{ label: '不限', value: null }] : []
  
  if (dictOptions[dictCode]) {
    const dictItems = dictOptions[dictCode].map(item => ({
      label: item.option_label,
      value: item.option_value,
      type: item.type,
      color: item.color,
      sort: item.sort,
      remark: item.remark
    }))
    
    // 按sort字段排序
    dictItems.sort((a, b) => (a.sort || 0) - (b.sort || 0))
    
    options.push(...dictItems)
  }
  
  return options
}

/**
 * 根据字典值获取字典项配置
 * @param {string} dictCode - 字典编码
 * @param {string|number} value - 字典值
 * @returns {Object|null} 字典项配置
 */
export function getDictItem(dictCode, value) {
  if (!dictOptions[dictCode]) {
    return null
  }
  
  return dictOptions[dictCode].find(item => item.option_value === value) || null
}

/**
 * 根据字典值获取字典标签
 * @param {string} dictCode - 字典编码
 * @param {string|number} value - 字典值
 * @param {string} defaultLabel - 默认标签
 * @returns {string} 字典标签
 */
export function getDictLabel(dictCode, value, defaultLabel = '未知') {
  const dictItem = getDictItem(dictCode, value)
  return dictItem?.option_label || defaultLabel
}

/**
 * 根据字典值获取字典颜色
 * @param {string} dictCode - 字典编码
 * @param {string|number} value - 字典值
 * @param {string} defaultColor - 默认颜色
 * @returns {string} 字典颜色
 */
export function getDictColor(dictCode, value, defaultColor = '#909399') {
  const dictItem = getDictItem(dictCode, value)
  return dictItem?.color || defaultColor
}

/**
 * 根据字典值获取字典类型
 * @param {string} dictCode - 字典编码
 * @param {string|number} value - 字典值
 * @param {string} defaultType - 默认类型
 * @returns {string} 字典类型
 */
export function getDictType(dictCode, value, defaultType = 'default') {
  const dictItem = getDictItem(dictCode, value)
  return dictItem?.type || defaultType
}

/**
 * 车辆品牌相关工具函数
 */
export const vehicleBrandUtils = {
  /**
   * 获取车辆品牌选项
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 品牌选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('vehicle_brand', includeAll),
  
  /**
   * 获取品牌标签
   * @param {string} value - 品牌值
   * @returns {string} 品牌标签
   */
  getLabel: (value) => getDictLabel('vehicle_brand', value),
  
  /**
   * 获取品牌颜色
   * @param {string} value - 品牌值
   * @returns {string} 品牌颜色
   */
  getColor: (value) => getDictColor('vehicle_brand', value),
  
  /**
   * 获取品牌类型
   * @param {string} value - 品牌值
   * @returns {string} 品牌类型
   */
  getType: (value) => getDictType('vehicle_brand', value)
}

/**
 * 订单状态相关工具函数
 */
export const orderStatusUtils = {
  /**
   * 获取订单状态选项
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 状态选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('order_status', includeAll),
  
  /**
   * 获取状态标签
   * @param {string} value - 状态值
   * @returns {string} 状态标签
   */
  getLabel: (value) => getDictLabel('order_status', value),
  
  /**
   * 获取状态颜色
   * @param {string} value - 状态值
   * @returns {string} 状态颜色
   */
  getColor: (value) => getDictColor('order_status', value),
  
  /**
   * 获取状态类型
   * @param {string} value - 状态值
   * @returns {string} 状态类型
   */
  getType: (value) => getDictType('order_status', value)
}

/**
 * 库存状态相关工具函数
 */
export const stockStatusUtils = {
  /**
   * 获取库存状态选项
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 状态选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('stock_status', includeAll),
  
  /**
   * 获取状态标签
   * @param {string} value - 状态值
   * @returns {string} 状态标签
   */
  getLabel: (value) => getDictLabel('stock_status', value),
  
  /**
   * 获取状态颜色
   * @param {string} value - 状态值
   * @returns {string} 状态颜色
   */
  getColor: (value) => getDictColor('stock_status', value),
  
  /**
   * 获取状态类型
   * @param {string} value - 状态值
   * @returns {string} 状态类型
   */
  getType: (value) => getDictType('stock_status', value)
}
