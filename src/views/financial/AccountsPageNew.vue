<template>
  <div class="accounts-page-new">
    <org-tree-settings-layout
      title="账户列表"
      search-placeholder="请输入账户名称或账号"
      :columns="columns"
      :api-service="accountsApi"
      org-id-field="ownerOrgId"
      row-key="id"
      :default-new-row="defaultNewRow"
      @select-org="handleOrgSelect"
      @add-data="handleAddData"
      @edit-data="handleEditData"
      @save-data="handleSaveData"
      @delete-data="handleDeleteData"
    />
  </div>
</template>

<script setup>
import { h, ref, markRaw, shallowRef } from 'vue'
import { NTag, NInputNumber, NInput, NSwitch, NSpace, NButton, NIcon, NTooltip } from 'naive-ui'
import OrgTreeSettingsLayout from '@/components/layout/OrgTreeSettingsLayout.vue'
import { accountsApi } from '@/api/accounts'
import { formatMoney } from '@/utils/money'
import messages from '@/utils/messages'
import { CheckmarkCircleOutline, CloseCircleOutline } from '@vicons/ionicons5'
import { Edit, TrashCan } from '@vicons/carbon'

// 定义emit函数
const emit = defineEmits([
  'select-org',
  'add-data',
  'edit-data',
  'save-data',
  'cancel-edit',
  'delete-data',
  'refresh-data'
])

// 默认新行数据
const defaultNewRow = {
  abbr: '',
  receivable: true,
  payable: true,
  usable: true, // 账户状态，默认为启用
  initialAmount: 0, // 期初金额，默认0（用于编辑）
  initAmount: 0, // 期初金额，默认0（用于API）
  remark: '',
  editorName: JSON.parse(localStorage.getItem('user') || '{}').nickname || '当前用户'
}

// 格式化金额显示（分转元，添加千分位分隔符）
const formatAmount = (amount) => {
  // 将分转换为元并格式化
  return formatMoney(amount ? amount / 100 : 0)
}

// 表格列定义 - 使用markRaw避免Vue响应式处理
const columns = markRaw([
  {
    title: '账户名称',
    key: 'abbr',
    width: 200,
    fixed: 'left',
    ellipsis: {
      tooltip: true
    },
    render(row) {
      // 判断当前行是否处于编辑状态
      const isEditing = row.isEditing

      if (isEditing) {
        return h(NInput, {
          value: row.abbr,
          maxlength: 20,
          showCount: true,
          onUpdateValue(v) {
            row.abbr = v
          }
        })
      }

      return h('span', row.abbr)
    }
  },
  {
    title: '可收款',
    key: 'receivable',
    width: 100,
    render(row) {
      // 判断当前行是否处于编辑状态
      const isEditing = row.isEditing

      if (isEditing) {
        return h(NSwitch, {
          value: row.receivable,
          onUpdateValue(v) {
            row.receivable = v
          }
        })
      }

      return h(NTag, { type: row.receivable ? 'success' : 'default' }, {
        default: () => row.receivable ? '是' : '否'
      })
    }
  },
  {
    title: '可付款',
    key: 'payable',
    width: 100,
    render(row) {
      // 判断当前行是否处于编辑状态
      const isEditing = row.isEditing

      if (isEditing) {
        return h(NSwitch, {
          value: row.payable,
          onUpdateValue(v) {
            row.payable = v
          }
        })
      }

      return h(NTag, { type: row.payable ? 'success' : 'default' }, {
        default: () => row.payable ? '是' : '否'
      })
    }
  },
  {
    title: '账户状态',
    key: 'usable',
    width: 100,
    render(row) {
      // 判断当前行是否处于编辑状态
      const isEditing = row.isEditing

      if (isEditing) {
        return h(NSwitch, {
          value: row.usable,
          onUpdateValue(v) {
            row.usable = v
          }
        })
      }

      return h(NTag, { type: row.usable ? 'success' : 'error' }, {
        default: () => row.usable ? '启用' : '停用'
      })
    }
  },
  {
    title: '期初金额',
    key: 'initAmount',
    width: 150,
    render(row) {
      // 判断当前行是否处于编辑状态
      const isEditing = row.isEditing

      if (isEditing) {
        return h(NInputNumber, {
          value: (row.initialAmount || row.initAmount || 0) / 100, // 分转元
          min: -999999999, // 允许负数
          precision: 2, // 保留2位小数
          step: 1,
          buttonPlacement: 'both',
          onUpdateValue(v) {
            // 元转分，并保存
            row.initialAmount = Math.round((v || 0) * 100)
            // 同时更新initAmount字段，用于API提交
            row.initAmount = Math.round((v || 0) * 100)
          }
        })
      }

      // 显示为千分位格式
      return h('span', formatAmount(row.initAmount || 0))
    }
  },
  {
    title: '备注',
    key: 'remark',
    width: 250,
    render(row) {
      // 判断当前行是否处于编辑状态
      const isEditing = row.isEditing

      if (isEditing) {
        return h(NInput, {
          value: row.remark,
          maxlength: 100,
          showCount: true,
          onUpdateValue(v) {
            row.remark = v
          }
        })
      }

      return h('span', row.remark || '-')
    }
  },
  {
    title: '经办人',
    key: 'editorName',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right',
    render: (row) => {
      // 判断当前行是否处于编辑状态
      const isEditing = row.isEditing

      if (isEditing) {
        return h(NSpace, { align: 'center' }, {
          default: () => [
            h(NButton, {
              quaternary: true,
              circle: true,
              size: 'small',
              type: 'success',
              onClick: async () => {
                const result = await handleSaveData(row);
                if (result) {
                  emit('save-data', row);
                }
              },
              style: 'color: #18a058; font-size: 18px;'
            }, { default: () => h(NIcon, { component: CheckmarkCircleOutline }) }),
            h(NButton, {
              quaternary: true,
              circle: true,
              size: 'small',
              type: 'error',
              onClick: () => {
                // 清除本地存储的原始数据
                if (row.id) {
                  localStorage.removeItem(`original_row_${row.id}`);
                }
                emit('cancel-edit', row);
              },
              style: 'color: #d03050; font-size: 18px;'
            }, { default: () => h(NIcon, { component: CloseCircleOutline }) })
          ]
        })
      }

      return h(NSpace, { align: 'center' }, {
        default: () => [
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => {
              handleEditData(row);
              emit('edit-data', row);
            }
          }, { default: () => h(NIcon, { component: Edit }) }),
          h(NTooltip, {
            trigger: 'hover',
            placement: 'top',
            disabled: !row.usable,
          }, {
            default: () => row.usable ? '无法删除启用状态的账户' : '删除账户',
            trigger: () => h(
              NButton,
              {
                quaternary: true,
                circle: true,
                size: 'small',
                onClick: () => handleDeleteData(row) && emit('delete-data', row),
                disabled: row.usable === true,
                style: row.usable ? 'cursor: not-allowed; opacity: 0.5;' : ''
              },
              { default: () => h(NIcon, { component: TrashCan }) }
            )
          })
        ]
      })
    }
  }
])

// 事件处理函数
function handleOrgSelect(orgId) {
  console.log('Selected organization:', orgId)
}

function handleAddData(newRow) {
  console.log('Added new row:', newRow)
  // 标记为编辑状态，用于列渲染
  newRow.isEditing = true
}

function handleEditData(row) {
  console.log('Editing row:', row)
  // 保存原始数据，用于比较是否有变化
  localStorage.setItem(`original_row_${row.id}`, JSON.stringify({
    abbr: row.abbr,
    receivable: row.receivable,
    payable: row.payable,
    usable: row.usable,
    initAmount: row.initAmount,
    initialAmount: row.initialAmount,
    remark: row.remark
  }))
  // 标记为编辑状态，用于列渲染
  row.isEditing = true
}

// 比较数据是否有变化
function hasDataChanged(originalData, currentData) {
  // 比较关键字段是否有变化
  return originalData.abbr !== currentData.abbr ||
    originalData.receivable !== currentData.receivable ||
    originalData.payable !== currentData.payable ||
    originalData.usable !== currentData.usable ||
    originalData.initAmount !== currentData.initAmount ||
    originalData.initialAmount !== currentData.initialAmount ||
    originalData.remark !== currentData.remark
}

async function handleSaveData(row) {
  console.log('Saving row:', row)

  // 验证必填字段
  if (!row.abbr) {
    messages.error('账户名称不能为空')
    return false
  }

  // 验证字段长度
  if (row.abbr.length > 20) {
    messages.error('账户名称不能超过20个字符')
    return false
  }

  if (row.remark && row.remark.length > 100) {
    messages.error('备注不能超过100个字符')
    return false
  }

  // 验证期初金额
  if ((row.initialAmount === undefined || row.initialAmount === null) &&
      (row.initAmount === undefined || row.initAmount === null)) {
    messages.error('期初金额不能为空')
    return false
  }

  try {
    // 检查是否是临时ID（以temp_开头）
    const isTemporaryId = typeof row.id === 'string' && row.id.startsWith('temp_')
    let response = null

    if (row.isNew || isTemporaryId) {
      // 创建新行数据的副本，移除id字段
      const newRowData = { ...row }

      // 删除id字段，避免在新增时传入id
      if (newRowData.id) {
        delete newRowData.id
      }

      // 调用API创建账户
      response = await accountsApi.create(newRowData)

      if (response && response.code === 200) {
        messages.success('账户创建成功')

        // 获取当前选中的机构ID
        const currentOrgId = row.ownerOrgId

        // 刷新数据列表
        const params = {
          ownerOrgId: currentOrgId,
          page: 1,
          size: 20
        }

        const listResponse = await accountsApi.getList(params)

        // 更新本地数据列表
        if (listResponse && listResponse.code === 200 && listResponse.data) {
          // 通知父组件刷新数据
          emit('refresh-data')
        }
      } else {
        throw new Error(response?.message || '创建失败')
      }
    } else {
      // 检查数据是否有变化
      const originalData = JSON.parse(localStorage.getItem(`original_row_${row.id}`));

      // 如果没有原始数据或数据有变化，才发送更新请求
      if (!originalData || hasDataChanged(originalData, row)) {
        // 更新现有账户
        response = await accountsApi.update(row)

        if (response && response.code === 200) {
          messages.success('账户更新成功')
        } else {
          throw new Error(response?.message || '更新失败')
        }
      } 

      // 清除本地存储的原始数据
      localStorage.removeItem(`original_row_${row.id}`);
    }

    // 移除编辑状态标记
    row.isEditing = false
    return true
  } catch (error) {
    console.error('Failed to save account:', error)
    messages.error(error.message || '保存失败')
    return false
  }
}

function handleDeleteData(row) {
  console.log('Deleted row:', row)

  // 检查账户是否处于启用状态
  if (row.usable === true) {
    messages.warning('启用状态的账户不能删除，请先停用该账户')
    return false
  }

  return true
}
</script>

<style lang="scss">
@use './styles/accounts-page';
</style>
