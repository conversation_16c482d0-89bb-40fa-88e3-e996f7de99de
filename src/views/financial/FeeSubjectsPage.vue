<template>
  <div class="fee-subjects-page">
    <!-- 标题和工具栏 -->
    <n-space justify="space-between" align="center" class="page-header">
      <div class="title-container">
        <h2 class="section-title">费用科目设置</h2>
        <div class="divider"></div>
      </div>
      <n-space>
        <n-button type="primary" @click="handleRefresh">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
        <n-button type="info" @click="handleAddSubject">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增
        </n-button>
        <n-input
          v-model:value="searchParams.keywords"
          placeholder="搜索科目名称"
          style="width: 300px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
          <template #suffix>
            <span style="color: #999">支持模糊搜索</span>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="subjectsData"
      :loading="loading"
      :pagination="pagination"
      :row-key="row => row.id"
      :bordered="false"
      :single-line="false"
      :striped="true"
      size="medium"
    >
      <template #empty>
        <span>暂无数据</span>
      </template>
    </n-data-table>
  </div>
</template>

<script setup>
import { ref, reactive, h, onMounted } from 'vue'
import {
  NSpace,
  NButton,
  NInput,
  NIcon,
  NDataTable,
  NTag,
  NSwitch,
  NSelect,
  NInputNumber,
  useDialog,
  useMessage
} from 'naive-ui'
import {
  RefreshOutline,
  AddOutline,
  SearchOutline,
  CreateOutline,
  TrashOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline
} from '@vicons/ionicons5'
import { feeSubjectApi } from '@/api/feeSubject'
import messages from '@/utils/messages'
import { formatMoney } from '@/utils/money'

// 状态变量
const tableRef = ref(null)
const loading = ref(false)
const subjectsData = ref([])
const editableRowKeys = ref([]) // 当前可编辑的行的key集合

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page) => {
    pagination.page = page
    searchParams.page = page
    fetchSubjects()
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    searchParams.page = 1
    searchParams.size = pageSize
    fetchSubjects()
  }
})

// 搜索参数
const searchParams = reactive({
  keywords: '',
  page: 1,
  size: 20
})

// 对话框
const dialog = useDialog()
const message = useMessage()

// 科目类别选项
const categoryOptions = feeSubjectApi.getSubjectCategoryOptions()

// 表格列定义
const columns = [
  {
    title: '科目名称',
    key: 'subjectName',
    width: 180,
    ellipsis: {
      tooltip: true
    },
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NInput, {
          value: row.subjectName,
          maxlength: 20,
          showCount: true,
          onUpdateValue(v) {
            subjectsData.value[index].subjectName = v
          }
        })
      }

      return h('span', row.subjectName)
    }
  },
  {
    title: '科目类别',
    key: 'subjectCategory',
    width: 150,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSelect, {
          value: row.subjectCategory,
          options: categoryOptions,
          onUpdateValue(v) {
            subjectsData.value[index].subjectCategory = v
            // 更新类别名称
            const category = categoryOptions.find(item => item.value === v)
            if (category) {
              subjectsData.value[index].subjectCategoryName = category.label
            }
          }
        })
      }

      return h('span', row.subjectCategoryName)
    }
  },
  {
    title: '可收款',
    key: 'receivable',
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSwitch, {
          value: row.receivable,
          onUpdateValue(v) {
            subjectsData.value[index].receivable = v
          }
        })
      }

      return h(NTag, { type: row.receivable ? 'success' : 'default' }, {
        default: () => row.receivable ? '是' : '否'
      })
    }
  },
  {
    title: '可付款',
    key: 'payable',
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSwitch, {
          value: row.payable,
          onUpdateValue(v) {
            subjectsData.value[index].payable = v
          }
        })
      }

      return h(NTag, { type: row.payable ? 'success' : 'default' }, {
        default: () => row.payable ? '是' : '否'
      })
    }
  },
  {
    title: '科目状态',
    key: 'usable',
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSwitch, {
          value: row.usable !== false, // 默认为true
          onUpdateValue(v) {
            subjectsData.value[index].usable = v
          }
        })
      }

      return h(NTag, { type: row.usable !== false ? 'success' : 'error' }, {
        default: () => row.usable !== false ? '启用' : '停用'
      })
    }
  },
  {
    title: '默认金额',
    key: 'defaultAmount',
    width: 150,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NInputNumber, {
          value: row.defaultAmount / 100, // 分转元
          min: 0,
          precision: 2,
          step: 100,
          onUpdateValue(v) {
            // 元转分
            subjectsData.value[index].defaultAmount = v ? Math.round(v * 100) : 0
          }
        })
      }

      return h('span', formatMoney(row.defaultAmount / 100))
    }
  },
  {
    title: '经办人',
    key: 'editorName',
    width: 120,
    render(row) {
      return h('span', row.editorName || '-')
    }
  },
  {
    title: '备注',
    key: 'remark',
    ellipsis: {
      tooltip: true
    },
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NInput, {
          value: row.remark,
          maxlength: 100,
          showCount: true,
          onUpdateValue(v) {
            subjectsData.value[index].remark = v
          }
        })
      }

      return h('span', row.remark || '-')
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right',
    render: (row, index) => {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSpace, { align: 'center' }, {
          default: () => [
            h(NButton, {
              quaternary: true,
              circle: true,
              size: 'small',
              type: 'success',
              onClick: () => saveRow(row),
              style: 'color: #18a058; font-size: 18px;'
            }, { default: () => h(NIcon, { component: CheckmarkCircleOutline }) }),
            h(NButton, {
              quaternary: true,
              circle: true,
              size: 'small',
              type: 'error',
              onClick: () => cancelEdit(row),
              style: 'color: #d03050; font-size: 18px;'
            }, { default: () => h(NIcon, { component: CloseCircleOutline }) })
          ]
        })
      }

      return h(NSpace, { align: 'center' }, {
        default: () => [
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => editRow(row),
            style: 'color: #2080f0; font-size: 18px;'
          }, { default: () => h(NIcon, { component: CreateOutline }) }),
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => deleteRow(row, index),
            style: 'color: #d03050; font-size: 18px;'
          }, { default: () => h(NIcon, { component: TrashOutline }) })
        ]
      })
    }
  }
]

// 获取费用科目列表
async function fetchSubjects() {
  loading.value = true
  try {
    const response = await feeSubjectApi.getFeeSubjectList(searchParams)
    subjectsData.value = response.data.list
    pagination.itemCount = response.data.total
  } catch (error) {
    console.error('Failed to fetch fee subjects:', error)
    messages.error('获取费用科目列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  searchParams.page = 1
  fetchSubjects()
}

// 刷新
function handleRefresh() {
  fetchSubjects()
}

// 添加新科目
function handleAddSubject() {
  // 获取当前用户信息
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
  const nickname = currentUser.nickname || '当前用户'

  // 创建一个新的科目行
  const newSubject = {
    id: `temp_${Date.now()}`, // 临时ID，保存时会被替换
    subjectName: '',
    subjectCategory: 'OTHER_FEE',
    subjectCategoryName: '其他费用',
    receivable: true,
    payable: true,
    usable: true, // 默认启用
    defaultAmount: 0, // 默认金额，单位分
    remark: '',
    creatorName: nickname,
    editorName: nickname, // 经办人默认为当前用户
    isNew: true // 标记为新行
  }

  // 添加到数据列表
  subjectsData.value.unshift(newSubject)

  // 设置为编辑状态
  editableRowKeys.value.push(newSubject.id)
}

// 编辑行
function editRow(row) {
  editableRowKeys.value.push(row.id)
}

// 取消编辑
function cancelEdit(row) {
  // 从可编辑行集合中移除
  editableRowKeys.value = editableRowKeys.value.filter(id => id !== row.id)

  // 如果是新添加的行，则从数据中移除
  if (row.isNew) {
    subjectsData.value = subjectsData.value.filter(item => item.id !== row.id)
  } else {
    // 如果是编辑现有行，则重新获取数据
    fetchSubjects()
  }
}

// 保存行
async function saveRow(row) {
  // 验证必填字段
  if (!row.subjectName) {
    messages.error('科目名称不能为空')
    return
  }

  // 验证字段长度
  if (row.subjectName.length > 20) {
    messages.error('科目名称不能超过20个字符')
    return
  }

  if (row.remark && row.remark.length > 100) {
    messages.error('备注不能超过100个字符')
    return
  }

  try {
    // 获取当前用户信息
    const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
    const nickname = currentUser.nickname || '当前用户'

    if (row.isNew) {
      // 创建新科目
      const newSubject = {
        subjectName: row.subjectName,
        subjectCategory: row.subjectCategory,
        receivable: row.receivable,
        payable: row.payable,
        usable: row.usable !== false, // 确保有值
        defaultAmount: row.defaultAmount,
        remark: row.remark,
        editorName: nickname // 经办人为当前用户
      }

      // 调用API创建科目
      await feeSubjectApi.createFeeSubject(newSubject)
      messages.success('科目创建成功')
    } else {
      // 更新现有科目
      const updatedSubject = {
        id: row.id,
        subjectName: row.subjectName,
        subjectCategory: row.subjectCategory,
        receivable: row.receivable,
        payable: row.payable,
        usable: row.usable !== false, // 确保有值
        defaultAmount: row.defaultAmount,
        remark: row.remark,
        editorName: nickname // 经办人更新为当前用户
      }

      // 调用API更新科目
      await feeSubjectApi.updateFeeSubject(updatedSubject)
      messages.success('科目更新成功')
    }

    // 从可编辑行集合中移除
    editableRowKeys.value = editableRowKeys.value.filter(id => id !== row.id)

    // 重新获取数据
    await fetchSubjects()
  } catch (error) {
    console.error('Failed to save fee subject:', error)
    messages.error('保存科目失败')
  }
}

// 删除行
function deleteRow(row, index) {
  dialog.warning({
    title: '删除确认',
    content: `确定要删除科目"${row.subjectName}"吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        if (!row.isNew) {
          // 如果不是新行，则调用API删除
          await feeSubjectApi.deleteFeeSubject(row.id)
        }

        // 从数据中移除
        subjectsData.value.splice(index, 1)
        messages.success('科目删除成功')
      } catch (error) {
        console.error('Failed to delete fee subject:', error)
        messages.error('删除科目失败')
      }
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchSubjects()
})
</script>

<style scoped>
.fee-subjects-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 4px 0;
}

.divider {
  height: 3px;
  width: 100%;
  background: linear-gradient(to right, var(--primary-color) 0%, rgba(0, 0, 0, 0.05) 100%);
  border-radius: 3px;
}
</style>