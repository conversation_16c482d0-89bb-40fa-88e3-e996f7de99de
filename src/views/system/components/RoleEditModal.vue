<template>
  <n-modal
    v-model:show="visible"
    :title="isEdit ? '编辑角色' : '新增角色'"
    preset="card"
    :mask-closable="false"
    :auto-focus="false"
    :transformOrigin="'center'"
    class="role-edit-modal"
    :style="{ width: '100vw', height: '100vh' }"
  >
    <div class="role-edit-container">
      <!-- 左侧表单区域 -->
      <div class="form-section">
        <div class="form-header">
          <h3>角色信息</h3>
        </div>
        <div class="form-content">
          <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="top"
            label-width="auto"
            require-mark-placement="right-hanging"
          >
            <n-form-item label="角色名称" path="roleName">
              <n-input 
                v-model:value="formData.roleName" 
                placeholder="请输入角色名称"
                size="large"
              />
            </n-form-item>
            <n-form-item label="角色代码" path="roleCode">
              <n-input
                v-model:value="formData.roleCode"
                placeholder="请输入角色代码"
                size="large"
                @blur="formData.roleCode = formData.roleCode?.trim() || ''"
              />
            </n-form-item>
          </n-form>
        </div>
      </div>

      <!-- 右侧权限树区域 -->
      <div class="permissions-section">
        <div class="permissions-header">
          <h3>系统权限</h3>
          <div class="permissions-actions">
            <n-button size="small" @click="expandAll">全部展开</n-button>
            <n-button size="small" @click="collapseAll">全部收起</n-button>
            <n-button size="small" @click="checkAll">全选</n-button>
            <n-button size="small" @click="uncheckAll">取消全选</n-button>
          </div>
        </div>
        <div class="permissions-content">
          <n-tree
            ref="menuTreeRef"
            :data="menuTree"
            checkable
            cascade
            :key-field="'id'"
            :label-field="'menuLabel'"
            :children-field="'subMenus'"
            :checked-keys="formData.menus"
            @update:checked-keys="handleMenuCheck"
            :default-expand-all="true"
            :selectable="false"
            :show-irrelevant-nodes="false"
            :expanded-keys="expandedKeys"
            @update:expanded-keys="handleExpandedKeysChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <n-space justify="end" size="large">
          <n-button size="large" @click="handleCancel">取消</n-button>
          <n-button type="primary" size="large" @click="handleSave">确定</n-button>
        </n-space>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { useRoleEditModal } from './RoleEditModal.js';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    required: true
  },
  menuTree: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:visible', 'save', 'cancel']);

// 使用composable函数
const {
  formRef,
  menuTreeRef,
  rules,
  expandedKeys,
  handleMenuCheck,
  handleExpandedKeysChange,
  expandAll,
  collapseAll,
  checkAll,
  uncheckAll,
  handleSave,
  handleCancel
} = useRoleEditModal(props, emit);

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});
</script>

<style lang="scss" scoped>
@use './RoleEditModal.scss';
</style>
