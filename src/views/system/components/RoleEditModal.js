import { ref, watch, nextTick } from 'vue';
import messages from '@/utils/messages';

export function useRoleEditModal(props, emit) {
  // 响应式数据
  const formRef = ref(null);
  const menuTreeRef = ref(null);
  const expandedKeys = ref([]);

  // 表单验证规则
  const rules = {
    roleName: [
      {
        required: true,
        message: '请输入角色名称（2-20个汉字）',
        trigger: ['blur', 'input'],
        validator: (_rule, value) => {
          if (!value) {
            return new Error('角色名称不能为空');
          } else if (value.length < 2 || value.length > 20) {
            return new Error('角色名称长度应在2-20个字符之间');
          }
          return true;
        }
      }
    ],
    roleCode: [
      {
        required: true,
        message: '请输入角色代码（2-20个大写字母或下划线）',
        trigger: ['blur', 'input'],
        validator: (_rule, value) => {
          if (!value || value.trim() === '') {
            return new Error('角色代码不能为空');
          } else if (!/^[a-zA-Z_]{2,20}$/.test(value)) {
            return new Error('角色代码应为2-20个大写字母或下划线');
          }
          return true;
        }
      }
    ],
    menus: [
      {
        type: 'array',
        required: true,
        message: '请至少选择一个菜单权限',
        trigger: 'change',
        validator: (_rule, value) => {
          if (!value || value.length === 0) {
            return new Error('请至少选择一个菜单权限');
          }
          return true;
        }
      }
    ]
  };

  // 获取所有节点的key（用于全部展开/收起）
  const getAllNodeKeys = (nodes) => {
    const keys = [];
    const traverse = (nodeList) => {
      nodeList.forEach(node => {
        keys.push(node.id);
        if (node.subMenus && node.subMenus.length > 0) {
          traverse(node.subMenus);
        }
      });
    };
    traverse(nodes);
    return keys;
  };

  // 获取所有叶子节点的key（用于全选）
  const getAllLeafKeys = (nodes) => {
    const keys = [];
    const traverse = (nodeList) => {
      nodeList.forEach(node => {
        if (!node.subMenus || node.subMenus.length === 0) {
          keys.push(node.id);
        } else {
          traverse(node.subMenus);
        }
      });
    };
    traverse(nodes);
    return keys;
  };

  // 处理菜单选择
  const handleMenuCheck = (checkedKeys) => {
    props.formData.menus = checkedKeys;
    // 触发表单验证
    if (formRef.value) {
      formRef.value.validate()
        .then(() => {
          // 验证成功
        })
        .catch((errors) => {
          // 验证失败，处理错误
          console.error('表单验证失败:', errors);
        });
    }
  };

  // 处理展开状态变化
  const handleExpandedKeysChange = (keys) => {
    expandedKeys.value = keys;
  };

  // 全部展开
  const expandAll = () => {
    if (props.menuTree && props.menuTree.length > 0) {
      expandedKeys.value = getAllNodeKeys(props.menuTree);
    }
  };

  // 全部收起
  const collapseAll = () => {
    expandedKeys.value = [];
  };

  // 全选
  const checkAll = () => {
    if (props.menuTree && props.menuTree.length > 0) {
      const allKeys = getAllLeafKeys(props.menuTree);
      props.formData.menus = allKeys;
      // 由于使用了cascade，选中叶子节点会自动选中父节点
    }
  };

  // 取消全选
  const uncheckAll = () => {
    props.formData.menus = [];
  };

  // 保存
  const handleSave = async () => {
    if (!formRef.value) return;

    try {
      // 验证表单
      await formRef.value.validate();
      
      // 确保 roleCode 字段不为空
      if (!props.formData.roleCode || props.formData.roleCode.trim() === '') {
        messages.error('角色代码不能为空');
        return;
      }

      // 确保至少选择了一个菜单权限
      if (!props.formData.menus || props.formData.menus.length === 0) {
        messages.error('请至少选择一个菜单权限');
        return;
      }

      // 触发保存事件
      emit('save');
    } catch (errors) {
      console.error('表单验证失败:', errors);
      messages.error('请检查表单填写是否正确');
    }
  };

  // 取消
  const handleCancel = () => {
    emit('cancel');
  };

  // 监听弹窗显示状态，初始化展开状态
  watch(() => props.visible, (newValue) => {
    if (newValue && props.menuTree && props.menuTree.length > 0) {
      nextTick(() => {
        expandAll();
      });
    }
  });

  // 监听菜单树变化，更新展开状态
  watch(() => props.menuTree, (newValue) => {
    if (newValue && newValue.length > 0 && props.visible) {
      nextTick(() => {
        expandAll();
      });
    }
  }, { deep: true });

  return {
    formRef,
    menuTreeRef,
    rules,
    expandedKeys,
    handleMenuCheck,
    handleExpandedKeysChange,
    expandAll,
    collapseAll,
    checkAll,
    uncheckAll,
    handleSave,
    handleCancel
  };
}
