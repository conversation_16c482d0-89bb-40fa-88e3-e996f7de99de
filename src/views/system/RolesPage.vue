<template>
  <div class="role-management">
    <!-- 工具栏 -->
    <n-space class="toolbar">
      <n-button type="primary" @click="refreshRoles" round>
        <template #icon>
          <n-icon>
            <RefreshOutline />
          </n-icon>
        </template>
        刷新数据
      </n-button>
      <n-button type="info" @click="showAddDialog" round>
        <!-- 修改按钮颜色为蓝色 -->
        <template #icon>
          <n-icon>
            <AddOutline />
          </n-icon>
        </template>
        新增角色
      </n-button>
      <n-button
        type="error"
        @click="batchDelete"
        :disabled="!selectedRoles.length"
        round
      >
        <template #icon>
          <n-icon>
            <TrashOutline />
          </n-icon>
        </template>
        批量删除
      </n-button>
    </n-space>

    <!-- 角色列表 -->
    <n-data-table
      :columns="columns"
      :data="roles"
      :row-key="(row) => row.id"
      @update:checked-row-keys="handleSelectionChange"
    />

    <!-- 编辑角色对话框 -->
    <role-edit-modal
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :form-data="form"
      :menu-tree="menuTree"
      @save="handleSave"
      @cancel="dialogVisible = false"
    />
  </div>
</template>

<script setup>
import { useRolesPage } from "./RolesPage.js";
import RoleEditModal from "./components/RoleEditModal.vue";

// 使用composable函数获取所有数据和方法
const {
  // 响应式数据
  roles,
  menuTree,
  dialogVisible,
  isEdit,
  selectedRoles,
  form,
  columns,

  // 图标组件
  RefreshOutline,
  AddOutline,
  TrashOutline,

  // 方法
  refreshRoles,
  showAddDialog,
  batchDelete,
  handleSelectionChange,
  handleSave,
} = useRolesPage();
</script>

<style lang="scss" scoped>
@use "./RolesPage.scss";
</style>