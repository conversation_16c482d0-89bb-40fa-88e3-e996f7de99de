<template>
  <div class="test-page">
    <h2>业务机构选择器测试页面</h2>
    
    <div class="test-section">
      <n-button type="primary" @click="openSelector">打开机构选择器</n-button>
      
      <div v-if="selectedOrgs.length > 0" class="selected-result">
        <h3>选中的机构：</h3>
        <ul>
          <li v-for="org in selectedOrgs" :key="org.id">
            {{ org.orgName }} (ID: {{ org.id }})
          </li>
        </ul>
      </div>
    </div>

    <!-- 业务机构选择器 -->
    <BizOrgSelector
      :visible="showSelector"
      @update:visible="showSelector = $event"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { NButton } from 'naive-ui'
import BizOrgSelector from '@/components/bizOrg/BizOrgSelector.vue'

// 状态
const showSelector = ref(false)
const selectedOrgs = ref([])

// 打开选择器
const openSelector = () => {
  showSelector.value = true
}

// 处理机构选择
const handleOrgSelect = (orgs) => {
  console.log('测试页面收到选中的机构:', orgs)
  selectedOrgs.value = orgs
}

// 处理取消
const handleOrgCancel = () => {
  console.log('用户取消了选择')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-section {
  margin: 20px 0;
}

.selected-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.selected-result h3 {
  margin-top: 0;
  color: #333;
}

.selected-result ul {
  margin: 10px 0;
  padding-left: 20px;
}

.selected-result li {
  margin: 5px 0;
  color: #666;
}
</style>
