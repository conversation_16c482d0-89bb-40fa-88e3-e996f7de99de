<template>
  <div class="afmu-dashboard">
    <!-- 筛选区域 -->
    <n-card title="筛选条件" class="filter-section">
      <n-space vertical :size="24">
        <n-space align="center">
          <span class="label">教员职称</span>
          <n-space align="center">
            <n-tag v-for="title in titles" :key="title.value" 
                  :type="selectedTitle === title.value ? 'primary' : 'default'"
                  @click="handleTitleSelect(title.value)" 
                  class="filter-tag"
                  style="cursor: pointer">
              {{ title.label }}
            </n-tag>
          </n-space>
        </n-space>
        <n-space align="center">
          <span class="label">填报单位</span>
          <n-space align="center">
            <n-tag v-for="unit in visibleUnits" :key="unit.value"
                  :type="selectedUnit === unit.value ? 'primary' : 'default'"
                  @click="handleUnitSelect(unit.value)"
                  class="filter-tag"
                  style="cursor: pointer">
              {{ unit.label }}
            </n-tag>
            <n-button text type="primary" v-if="showMoreUnits" @click="toggleUnits" class="toggle-btn">
              {{ isUnitsExpanded ? '收起' : '更多...' }}
            </n-button>
          </n-space>
        </n-space>
      </n-space>
    </n-card>

    <!-- 预警列表 -->
    <n-card title="预警列表" class="warning-section">
      <n-list>
        <n-list-item v-for="warning in warningList" :key="warning.unit">
          <n-space justify="space-between">
            <span>{{ warning.unit }}</span>
            <n-space>
              <span class="warning-count">{{ warning.count }}</span>
              <n-button text type="primary" @click="showWarningDetail(warning)">
                查看预警详情
              </n-button>
            </n-space>
          </n-space>
        </n-list-item>
      </n-list>
    </n-card>

    <!-- 统计指标 -->
    <n-grid :cols="3" :x-gap="12" :y-gap="24" class="statistics-section">
      <n-grid-item v-for="stat in statistics" :key="stat.title">
        <n-card>
          <n-statistic :label="stat.title" :value="stat.value" />
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 图表区域 -->
    <n-grid :cols="1" :x-gap="12" :y-gap="24">
      <n-grid-item>
        <n-card title="单位开课分组统计">
          <div ref="unitCoursesChart" style="height: 400px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="单位专题授课统计">
          <div ref="specialCoursesChart" style="height: 400px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-grid :cols="1" :x-gap="12" :y-gap="24">
      <n-grid-item>
        <n-card title="课程专业统计TOP10">
          <div ref="courseTop10Chart" style="height: 400px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-grid :cols="2" :x-gap="12" :y-gap="24" class="statistics-section">
      <n-grid-item>
        <n-card title="教员职称分布">
          <div ref="titlePieChart" style="height: 400px"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="教员学历分布">
          <div ref="educationPieChart" style="height: 400px"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 预警详情弹窗 -->
    <n-modal v-model:show="showWarningModal" preset="card" style="width: 900px" title="预警详情">
      <n-descriptions :column="1" label-placement="left">
        <n-descriptions-item label="所属单位">{{ currentWarning?.unit }}</n-descriptions-item>
      </n-descriptions>
      <n-data-table :columns="warningColumns" :data="currentWarning?.details || []" />
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'

// 筛选数据
const titles = ref([
  { label: '不限', value: 'all' },
  { label: '教授', value: 'professor' },
  { label: '副教授', value: 'associate' },
  { label: '讲师', value: 'lecturer' },
  { label: '其他', value: 'other' }
])

const units = ref([
  { label: '不限', value: 'all' },
  { label: '单位1', value: 'unit1' },
  { label: '单位2', value: 'unit2' },
  { label: '单位3', value: 'unit3' },
  { label: '单位4', value: 'unit4' },
  { label: '单位5', value: 'unit5' },
  { label: '单位6', value: 'unit6' },
  { label: '单位7', value: 'unit7' },
  { label: '单位8', value: 'unit8' },
  { label: '单位9', value: 'unit9' },
  { label: '单位10', value: 'unit10' },
  { label: '单位11', value: 'unit11' },
  { label: '单位12', value: 'unit12' },
  { label: '单位13', value: 'unit13' },
  { label: '单位14', value: 'unit14' },
  { label: '单位15', value: 'unit15' },
  { label: '单位16', value: 'unit16' },
  { label: '单位17', value: 'unit17' },
  { label: '单位18', value: 'unit18' },
  { label: '单位19', value: 'unit19' },
  { label: '单位20', value: 'unit20' }
])

const selectedTitle = ref('all')
const selectedUnit = ref('all')
const isUnitsExpanded = ref(false)
const VISIBLE_UNITS_COUNT = 5

const visibleUnits = computed(() => {
  return isUnitsExpanded.value ? units.value : units.value.slice(0, VISIBLE_UNITS_COUNT)
})

const showMoreUnits = computed(() => units.value.length > VISIBLE_UNITS_COUNT)

// 预警列表数据
const warningList = ref([
  { unit: '填报单位1', count: 5 },
  { unit: '填报单位2', count: 3 },
  { unit: '填报单位3', count: 1 },
  { unit: '填报单位4', count: 2 },
  { unit: '填报单位5', count: 4 },
  { unit: '填报单位6', count: 1 },
  { unit: '填报单位7', count: 3 },
  { unit: '填报单位8', count: 2 },
  { unit: '填报单位9', count: 1 },
  { unit: '填报单位10', count: 4 }
])

// 统计指标数据
const statistics = ref([
  { title: '已填报单位', value: 15 },
  { title: '填报中单位', value: 3 },
  { title: '未填报单位', value: 2 },
  { title: '教授人数', value: 45 },
  { title: '副教授人数', value: 85 },
  { title: '其他人数', value: 420 },
  { title: '开课总量', value: 350 },
  { title: '主干课程', value: 280 },
  { title: '专题授课', value: 70 }
])

// 预警详情弹窗
const showWarningModal = ref(false)
const currentWarning = ref(null)
const warningColumns = [
  { title: '授课教员', key: 'teacher' },
  { title: '教员职称', key: 'title' },
  { title: '专题授课记录', key: 'specialCourses' },
  { title: '非专题授课学时', key: 'normalCourses' },
  { title: '预警原因', key: 'reason', width: 400 }
]

// 方法
const handleTitleSelect = (title) => {
  selectedTitle.value = title
}

const handleUnitSelect = (unit) => {
  selectedUnit.value = unit
}

const toggleUnits = () => {
  isUnitsExpanded.value = !isUnitsExpanded.value
}

const showWarningDetail = (warning) => {
  currentWarning.value = {
    unit: warning.unit,
    details: [
      {
        teacher: '刘志昊',
        title: '教授',
        specialCourses: 0,
        normalCourses: 15,
        reason: '无专题授课记录，且非专题授课记录中，理论及实践学时小于16'
      },
      {
        teacher: '刘长江',
        title: '教授',
        specialCourses: 0,
        normalCourses: 11,
        reason: '无专题授课记录，且非专题授课记录中，理论及实践学时小于16'
      }
    ]
  }
  showWarningModal.value = true
}

// 课程专业统计TOP10数据
const courseTop10Data = ref([
  { courseName: '计算机网络', teacher: '张三', title: '教授', totalHours: 48, studentCount: 150 },
  { courseName: '数据结构', teacher: '李四', title: '副教授', totalHours: 64, studentCount: 145 },
  { courseName: '操作系统', teacher: '王五', title: '教授', totalHours: 56, studentCount: 140 },
  { courseName: '软件工程', teacher: '赵六', title: '副教授', totalHours: 48, studentCount: 135 },
  { courseName: '数据库原理', teacher: '钱七', title: '教授', totalHours: 56, studentCount: 130 },
  { courseName: '计算机组成原理', teacher: '孙八', title: '副教授', totalHours: 64, studentCount: 125 },
  { courseName: '人工智能导论', teacher: '周九', title: '教授', totalHours: 48, studentCount: 120 },
  { courseName: '编译原理', teacher: '吴十', title: '副教授', totalHours: 56, studentCount: 115 },
  { courseName: '算法设计与分析', teacher: '郑十一', title: '教授', totalHours: 48, studentCount: 110 },
  { courseName: '机器学习', teacher: '王十二', title: '副教授', totalHours: 56, studentCount: 105 }
])

// 图表初始化
const courseTop10Chart = ref(null)
const unitCoursesChart = ref(null)
const specialCoursesChart = ref(null)
const titlePieChart = ref(null)
const educationPieChart = ref(null)

onMounted(() => {
  // 初始化图表
  const charts = [
    { ref: unitCoursesChart, options: getUnitCoursesOptions() },
    { ref: specialCoursesChart, options: getSpecialCoursesOptions() },
    { ref: titlePieChart, options: getTitlePieOptions() },
    { ref: educationPieChart, options: getEducationPieOptions() },
    { ref: courseTop10Chart, options: getCourseTop10Options() }
  ]

  charts.forEach(({ ref: chartRef, options }) => {
    const chart = echarts.init(chartRef.value)
    chart.setOption(options)
  })
})

// 图表配置
const getUnitCoursesOptions = () => ({
  tooltip: { trigger: 'axis' },
  legend: { data: ['总数', '主干', '专题'] },
  xAxis: {
    type: 'category',
    data: ['单位1', '单位2', '单位3', '单位4', '单位5', '单位6', '单位7', '单位8', '单位9', '单位10']
  },
  yAxis: { type: 'value' },
  series: [
    { name: '总数', type: 'bar', data: [20, 15, 18, 12, 16, 22, 19, 14, 17, 21] },
    { name: '主干', type: 'bar', data: [15, 12, 14, 8, 11, 18, 15, 10, 13, 16] },
    { name: '专题', type: 'bar', data: [5, 3, 4, 4, 5, 4, 4, 4, 4, 5] }
  ]
})

const getSpecialCoursesOptions = () => ({
  tooltip: { trigger: 'axis' },
  xAxis: {
    type: 'category',
    data: ['单位1', '单位2', '单位3', '单位4', '单位5', '单位6', '单位7', '单位8', '单位9', '单位10']
  },
  yAxis: { type: 'value' },
  series: [{
    data: [5, 3, 4, 4, 5, 4, 4, 4, 4, 5],
    type: 'bar'
  }]
})

const getTitlePieOptions = () => ({
  tooltip: { trigger: 'item' },
  legend: { orient: 'vertical', left: 'left' },
  series: [{
    type: 'pie',
    radius: '50%',
    data: [
      { value: 20, name: '教授' },
      { value: 45, name: '副教授' },
      { value: 385, name: '其他' }
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
})

const getEducationPieOptions = () => ({
  tooltip: { trigger: 'item' },
  legend: { orient: 'vertical', left: 'left' },
  series: [{
    type: 'pie',
    radius: '50%',
    data: [
      { value: 150, name: '博士' },
      { value: 200, name: '硕士' },
      { value: 100, name: '其他' }
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
})

// 课程TOP10图表配置
const getCourseTop10Options = () => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' }
  },
  grid: {
    left: '25%',
    right: '5%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    name: '上课人数'
  },
  yAxis: {
    type: 'category',
    data: courseTop10Data.value.map(item => 
      `${item.courseName}\n${item.teacher} (${item.title})\n${item.totalHours}学时`
    ).reverse(),
    axisLabel: {
      interval: 0,
      formatter: (value) => value.split('\n')
    }
  },
  series: [{
    name: '上课人数',
    type: 'bar',
    data: courseTop10Data.value.map(item => item.studentCount).reverse(),
    label: {
      show: true,
      position: 'right'
    }
  }]
})
</script>

<style scoped>
.afmu-dashboard {
  padding: 20px;
}

.afmu-dashboard > * {
  margin-bottom: 32px;
}

.filter-section .label {
  font-weight: bold;
  min-width: 80px;
  display: inline-block;
  font-size: 16px;
  line-height: 40px;
}

.filter-section .filter-tag {
  padding: 8px 20px;
  font-size: 16px;
  line-height: 24px;
}

.filter-section .toggle-btn {
  font-size: 16px;
  line-height: 40px;
  height: 40px;
  padding: 0 12px;
}

.warning-count {
  font-weight: bold;
  color: #f56c6c;
}

.statistics-section {
  margin: 32px 0;
}
</style>