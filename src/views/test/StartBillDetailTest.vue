<template>
  <div class="test-page">
    <h1>启票详情组件测试</h1>
    
    <n-space vertical>
      <n-input-group>
        <n-input v-model:value="billId" placeholder="输入启票ID" />
        <n-button type="primary" @click="showDetail">查看详情</n-button>
      </n-input-group>
      
      <n-alert title="使用说明" type="info">
        输入有效的启票ID，点击"查看详情"按钮测试组件
      </n-alert>
    </n-space>
    
    <!-- 使用我们的组件 -->
    <start-bill-detail-modal 
      v-model:visible="detailVisible" 
      :id="billId"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import StartBillDetailModal from '@/components/inventory/StartBillDetailModal.vue'

const billId = ref('')
const detailVisible = ref(false)

const showDetail = () => {
  if (billId.value) {
    detailVisible.value = true
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  margin-bottom: 20px;
}
</style>
