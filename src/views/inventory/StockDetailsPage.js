import { ref, reactive, computed, h } from 'vue'
import messages from '@/utils/messages'
import stocksApi from '@/api/stocks'
import { getDictOptions } from '@/api/dict'
import { NIcon, NTag, NSelect, useDialog } from 'naive-ui'
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  EyeOutline,
  CreateOutline,
  ContractOutline,
  ExpandOutline,
  SwapHorizontalOutline,
  CopyOutline
} from '@vicons/ionicons5'
import { Building } from '@vicons/tabler'
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
  handleCustomDateChange as handleCustomDate
} from '@/utils/dateRange'
import FileUploadButton from '@/components/FileUploadButton.vue'
import StartBillDetailModal from '@/components/inventory/StartBillDetailModal.vue'
import BizOrgSelector from '@/components/bizOrg/BizOrgSelector.vue'

export default function useStockDetailsPage() {
  // 初始化对话框
  const dialog = useDialog()
  window.$dialog = dialog

  // 状态变量
  const tableRef = ref(null)
  const formRef = ref(null)
  const loading = ref(false)
  const dialogVisible = ref(false)
  const dialogTitle = ref('新增入库单')
  const isEdit = ref(false)

  // 弹窗最大化状态
  const isEditDialogMaximized = ref(true)

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)

  // 窗口高度响应式变量
  const windowHeight = ref(window.innerHeight)

  // 计算表格最大高度 - 用于冻结表头
  const tableMaxHeight = computed(() => {
    // 计算可用高度：视窗高度 - 页面padding - 筛选区域 - 工具栏 - 分页区域
    const pagepadding = 32 // 页面上下padding
    const filterHeight = 120 // 筛选区域高度
    const toolbarHeight = 60 // 工具栏高度
    const paginationHeight = 50 // 分页区域高度（优化后）
    const margin = 16 // 额外边距（减少）

    const calculatedHeight =
      windowHeight.value -
      pagepadding -
      filterHeight -
      toolbarHeight -
      paginationHeight -
      margin

    // 确保最小高度
    return Math.max(calculatedHeight, 300)
  })

  // 切换弹窗最大化/最小化
  const toggleEditDialogSize = () => {
    isEditDialogMaximized.value = !isEditDialogMaximized.value
  }

  // 车辆类别选项 - 从字典数据中加载
  const vehicleCategoryOptions = ref([
    { label: '不限', value: null }
  ])

  // 仓储状态选项 - 从字典数据中加载
  const stockStatusOptions = ref([
    { label: '不限', value: null }
  ])

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    vehicleCategory: null,
    invoiceOrgs: [], // 改为数组以支持多选
    stockStatus: null, // 仓储状态
    minAmount: null,
    maxAmount: null,
    keywords: ''
  })

  // 表单数据
  const form = reactive({
    id: null,
    batchCode: '',
    erpOrderDate: null,
    invoiceOrgName: '',
    vehicleCategory: '',
    vehicleSeries: '',
    vehicleModelCode: '',
    vehicleModelName: '',
    vin: '',
    startBillPrice: null,
    investmentEntity: '',
    paymentRemark: ''
  })

  // 表单验证规则
  const rules = {
    batchCode: {
      required: true,
      message: '请输入启票批次代码',
      trigger: ['blur', 'input']
    },
    erpOrderDate: {
      required: true,
      message: '请选择订单日期',
      trigger: ['blur', 'change']
    },
    invoiceOrgName: {
      required: true,
      message: '请输入启票单位',
      trigger: ['blur', 'input']
    },
    vehicleSeries: {
      required: true,
      message: '请输入车型系列',
      trigger: ['blur', 'input']
    },
    vin: {
      required: true,
      message: '请输入VIN码',
      trigger: ['blur', 'input']
    },
    startBillPrice: {
      required: true,
      message: '请输入库存成本',
      trigger: ['blur', 'change']
    }
  }

  // 数据列表
  const stocksData = ref([])

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 50,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0, // 总记录数
    showQuickJumper: false
  })

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const currentDetailId = ref(null)

  // 复制文本到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        messages.success('已复制到剪贴板')
      })
      .catch((err) => {
        console.error('复制失败:', err)
        messages.error('复制失败')
      })
  }

  // 计算属性：选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
      if (filterForm.invoiceOrgs.length === 1) {
        return filterForm.invoiceOrgs[0].orgName
      } else {
        return `已选择 ${filterForm.invoiceOrgs.length} 个机构`
      }
    }
    return "选择仓储单位"
  })

  // 计算属性：状态映射表 - 从字典数据生成
  const statusMap = computed(() => {
    const map = {}
    stockStatusOptions.value.forEach(item => {
      if (item.value !== null) {
        // 根据字典数据中的type和color属性设置标签样式
        const dictItem = stockStatusOptions.value.find(opt => opt.value === item.value)
        map[item.value] = {
          text: item.label,
          type: getTagTypeFromDictType(item.value),
          color: dictItem?.color
        }
      }
    })
    return map
  })

  // 根据状态值获取标签类型
  const getTagTypeFromDictType = (statusValue) => {
    switch (statusValue) {
      case 'stocking':
        return 'success'
      case 'transiting':
        return 'info'
      case 'sold':
        return 'warning'
      case 'returned':
        return 'error'
      default:
        return 'default'
    }
  }

  // 窗口大小变化处理
  const handleResize = () => {
    windowHeight.value = window.innerHeight
  }

  // 表格数据
  const filteredData = computed(() => {
    return stocksData.value
  })

  // 表格列配置
  const columns = [
    {
      title: 'VIN',
      key: 'vin',
      width: 220,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        return h(
          'div',
          {
            style: {
              alignItems: 'center',
              display: 'flex',
              cursor: 'pointer',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace'
            },
            onClick: () => copyToClipboard(row.vin),
            title: '点击复制VIN码'
          },
          [
            h('span', {
              style: {
                marginRight: '4px'
              }
            }, row.vin),
            h(
              NIcon,
              {
                size: 16,
                color: 'var(--primary-color)',
                style: {
                  opacity: 0.8,
                  transition: 'opacity 0.2s',
                  flexShrink: 0 // 防止图标被压缩
                },
                onMouseover: (e) => {
                  e.target.style.opacity = 1
                },
                onMouseout: (e) => {
                  e.target.style.opacity = 0.8
                }
              },
              { default: () => h(CopyOutline) }
            )
          ]
        )
      }
    },
    {
      title: '入库日期',
      key: 'createTime',
      width: 120,
      render(row) {
        if (!row.createTime) return '-'
        // 将日期格式化为yyyy-MM-dd格式
        const date = new Date(row.createTime)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }
    },
    {
      title: '仓储单位',
      key: 'stockOrgName',
      width: 280
    },
    {
      title: '采购单位',
      key: 'ownerOrgName',
      width: 280
    },
    {
      title: '库存状态',
      key: 'stockStatus',
      width: 100,
      render(row) {
        // 使用计算属性中的状态映射表
        const status = statusMap.value[row.stockStatus] || {
          text: row.stockStatus || '未知',
          type: 'default'
        }
        return h(
          NTag,
          {
            type: status.type,
            size: 'small',
            round: true,
            style: {
              padding: '0 10px',
              fontWeight: 'bold'
            }
          },
          { default: () => status.text }
        )
      }
    },
    {
      title: '库存成本(元)',
      key: 'stockAmount',
      width: 200,
      sorter: (a, b) => a.stockAmount - b.stockAmount,
      render(row) {
        // stockAmount单位是分，需要转换为元
        const amountInYuan = row.stockAmount / 100
        // 使用toLocaleString格式化为千分位，保留2位小数
        const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
        return h('span', { style: { fontWeight: 'bold' } }, `¥${formattedAmount}`)
      }
    },
    {
      title: '库龄(天)',
      key: 'stockDays',
      width: 100
    },
    {
      title: '品牌',
      key: 'brand',
      width: 100
    },
    {
      title: '车型',
      key: 'series',
      width: 200
    },
    {
      title: '配置',
      key: 'configName',
      width: 350
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right',
      align: 'center',
      ellipsis: false,
      render: (row) => {
        return h('div', { style: { display: 'flex', justifyContent: 'center', gap: '12px' } }, [
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#2080f0',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              onClick: () => handleView(row.id)
            },
            [h(NIcon, { size: 20 }, { default: () => h(EyeOutline) })]
          ),
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#18a058',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              onClick: () => handleEdit(row.id)
            },
            [h(NIcon, { size: 20 }, { default: () => h(CreateOutline) })]
          ),
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#f0a020',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              onClick: () => handleChangeStatus(row)
            },
            [h(NIcon, { size: 20 }, { default: () => h(SwapHorizontalOutline) })]
          )
        ])
      }
    }
  ]

  // 刷新数据
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.startDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.vehicleCategory = filterForm.vehicleCategory
      }

      // 处理仓储单位 - 支持多选，使用机构代码的逗号分隔格式
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        // 使用机构id作为机构代码，以逗号分隔的格式传入
        params.stock_org_code = filterForm.invoiceOrgs
          .map((org) => org.id)
          .join(",")
      }

      // 处理仓储状态
      if (filterForm.stockStatus) {
        params.stockStatus = filterForm.stockStatus
      }

      // 调用API获取数据
      const response = await stocksApi.getStocksList(params)

      if (response.code === 200) {
        // 直接使用返回的数据列表
        stocksData.value = response.data.list

        // 更新分页信息
        pagination.itemCount = response.data.total
        pagination.pageCount = response.data.pages

        // 确保当前页码与API返回的一致
        if (pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum
        }

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > response.data.pages && response.data.pages > 0) {
          pagination.page = response.data.pages
          refreshData()
          return
        }
      } else {
        messages.error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      messages.error('加载数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch)
  }

  // 处理自定义日期变化
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch)
  }

  // 处理查询
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  // 显示新增对话框
  const showAddDialog = () => {
    isEdit.value = false
    dialogTitle.value = '新增库存'

    // 重置表单
    Object.assign(form, {
      id: null,
      batchCode: '',
      erpOrderDate: null,
      invoiceOrgName: '',
      vehicleCategory: '',
      vehicleSeries: '',
      vehicleModelCode: '',
      vehicleModelName: '',
      vin: '',
      startBillPrice: null,
      investmentEntity: '',
      paymentRemark: ''
    })

    dialogVisible.value = true
  }

  // 处理编辑
  const handleEdit = async (id) => {
    try {
      loading.value = true
      console.log('编辑库存:', id)

      // 调用API获取详细数据
      const response = await stocksApi.getStockDetail(id)

      if (response.code === 200) {
        const apiData = response.data

        isEdit.value = true
        dialogTitle.value = '编辑库存'

        // 填充表单数据
        Object.assign(form, {
          id: apiData.id,
          erpOrderDate: apiData.createTime ? new Date(apiData.createTime) : null,
          invoiceOrgName: apiData.stockOrgName || '',
          vehicleCategory: '',
          vehicleSeries: '',
          vehicleModelCode: '',
          vehicleModelName: '',
          vin: apiData.vin || '',
          startBillPrice: apiData.stockAmount,
          investmentEntity: apiData.ownerOrgName || '',
          paymentRemark: ''
        })

        dialogVisible.value = true
      } else {
        messages.error(response.message || '获取库存数据失败')
      }
    } catch (error) {
      console.error('获取库存数据失败:', error)
      messages.error('获取库存数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理查看
  const handleView = (id) => {
    currentDetailId.value = id
    detailDialogVisible.value = true
  }

  // 处理保存
  const handleSave = () => {
    formRef.value?.validate(async (errors) => {
      if (errors) return

      try {
        loading.value = true

        // 准备请求数据
        const data = {
          ownerOrgName: form.investmentEntity,
          stockOrgName: form.invoiceOrgName,
          vin: form.vin,
          stockAmount: form.startBillPrice,
          stockStatus: 'stocking', // 默认状态为"在库"
          // 如果有组织ID，则添加
          ownerOrgId: filterForm.invoiceOrgs?.length > 0 ? filterForm.invoiceOrgs[0].id : 0,
          stockOrgId: filterForm.invoiceOrgs?.length > 0 ? filterForm.invoiceOrgs[0].id : 0,
          sbOrgId: 0 // 默认值
        }

        // 如果是编辑模式，添加ID
        if (isEdit.value) {
          data.id = form.id
        }

        // 调用保存API
        const response = isEdit.value
          ? await stocksApi.updateStock(data)
          : await stocksApi.addStock(data)

        if (response.code === 200) {
          messages.success(isEdit.value ? '更新成功' : '添加成功')
          dialogVisible.value = false
          // 刷新数据
          refreshData()
        } else {
          messages.error(response.message || (isEdit.value ? '更新失败' : '添加失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        messages.error('保存失败，请稍后重试')
      } finally {
        loading.value = false
      }
    })
  }

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  // 处理每页条数变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  // 处理导入成功
  const handleImportSuccess = async (fileInfo) => {
    try {
      messages.success(`文件上传成功: ${fileInfo.fileName}`)
      console.log('文件上传路径:', fileInfo.fileKey)

      // 调用车辆库存的文件解析接口
      const response = await stocksApi.importStocks(fileInfo.fileKey)

      if (response.code === 200) {
        messages.success('正在处理数据，请稍后刷新数据列表')
        setTimeout(() => {
          refreshData()
        }, 10000)
      } else {
        messages.error(response.message || '文件解析失败')
      }
    } catch (error) {
      console.error('文件解析失败:', error)
      messages.error('文件解析失败，请检查文件格式是否正确')
    }
  }

  // 处理导入错误
  const handleImportError = (errorMsg) => {
    console.error(`导入失败: ${errorMsg}`)
  }

  // 处理状态变更
  const handleChangeStatus = (row) => {
    // 使用字典数据中的状态选项（排除"不限"选项）
    const statusOptions = stockStatusOptions.value.filter(item => item.value !== null)

    const dialog = window.$dialog.create({
      title: '修改库存状态',
      content: () => {
        return h('div', [
          h('p', { style: { marginBottom: '16px' } }, `当前状态: ${statusOptions.find(item => item.value === row.stockStatus)?.label || '未知'}`),
          h('p', { style: { marginBottom: '8px' } }, '选择新状态:'),
          h(
            NSelect,
            {
              options: statusOptions,
              value: row.stockStatus,
              style: { width: '100%' },
              onUpdateValue: (value) => {
                dialog.destroy()
                updateStockStatus(row.id, value)
              }
            }
          )
        ])
      },
      positiveText: '取消',
      negativeText: null,
      autoFocus: false
    })
  }

  // 更新库存状态
  const updateStockStatus = async (id, status) => {
    try {
      loading.value = true
      const response = await stocksApi.updateStockStatus(id, status)

      if (response.code === 200) {
        messages.success('状态更新成功')
        refreshData()
      } else {
        messages.error(response.message || '状态更新失败')
      }
    } catch (error) {
      console.error('状态更新失败:', error)
      messages.error('状态更新失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理机构选择
  const handleOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      // 多选模式，保存所有选中的机构
      filterForm.invoiceOrgs = [...orgs]
      handleSearch()
    }
  }

  // 处理机构选择取消
  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  // 清空机构选择
  const clearOrgSelection = () => {
    filterForm.invoiceOrgs = []
    handleSearch()
  }

  // 移除单个机构
  const removeOrg = (orgToRemove) => {
    filterForm.invoiceOrgs = filterForm.invoiceOrgs.filter(
      (org) => org.id !== orgToRemove.id
    )
    handleSearch()
  }

  // 加载车辆品牌选项
  const loadVehicleBrandOptions = async () => {
    try {
      const res = await getDictOptions('vehicle_brand')
      if (res && res.code === 200 && res.data) {
        // 转换字典数据为选择器选项格式
        const options = res.data.map(item => ({
          label: item.option_label,
          value: item.option_value
        }))

        // 添加"不限"选项
        vehicleCategoryOptions.value = [
          { label: '不限', value: null },
          ...options
        ]
      }
    } catch (error) {
      console.error('获取车辆品牌选项失败:', error)
      messages.error('获取车辆品牌选项失败')
    }
  }

  // 加载仓储状态选项
  const loadStockStatusOptions = async () => {
    try {
      const res = await getDictOptions('stock_status')
      if (res && res.code === 200 && res.data) {
        // 转换字典数据为选择器选项格式
        const options = res.data.map(item => ({
          label: item.option_label,
          value: item.option_value
        }))

        // 添加"不限"选项
        stockStatusOptions.value = [
          { label: '不限', value: null },
          ...options
        ]
      }
    } catch (error) {
      console.error('获取仓储状态选项失败:', error)
      messages.error('获取仓储状态选项失败')
    }
  }

  // 初始化函数
  const initialize = async () => {
    window.addEventListener('resize', handleResize)
    // 加载字典数据
    await Promise.all([
      loadVehicleBrandOptions(),
      loadStockStatusOptions()
    ])
    refreshData()
  }

  // 清理函数
  const cleanup = () => {
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 组件引用
    FileUploadButton,
    StartBillDetailModal,
    BizOrgSelector,

    // 图标
    SearchOutline,
    RefreshOutline,
    AddOutline,
    EyeOutline,
    CreateOutline,
    ContractOutline,
    ExpandOutline,
    SwapHorizontalOutline,
    CopyOutline,
    Building,

    // 响应式数据
    tableRef,
    formRef,
    loading,
    dialogVisible,
    dialogTitle,
    isEdit,
    isEditDialogMaximized,
    vehicleCategoryOptions,
    stockStatusOptions,
    filterForm,
    form,
    rules,
    stocksData,
    pagination,
    windowHeight,
    detailDialogVisible,
    currentDetailId,
    showOrgSelector,

    // 计算属性
    tableMaxHeight,
    filteredData,
    columns,
    selectedOrgText,
    statusMap,

    // 日期相关
    dateRangeOptions,

    // 业务方法
    toggleEditDialogSize,
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    showAddDialog,
    handleEdit,
    handleView,
    handleSave,
    handlePageChange,
    handlePageSizeChange,
    handleImportSuccess,
    handleImportError,
    handleChangeStatus,
    refreshData,

    // 机构选择器相关方法
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,

    // 生命周期方法
    initialize,
    cleanup
  }
}
