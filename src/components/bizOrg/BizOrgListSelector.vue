<template>
  <div class="biz-org-list-selector">
    <n-card title="业务机构" class="org-list-card">
      <template #header-extra>
        <n-space>
          <n-button @click="refreshData" secondary size="small">
            <template #icon>
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>

      <!-- 搜索框 -->
      <div class="search-area">
        <n-input
          v-model:value="searchKeyword"
          placeholder="请输入机构名称搜索"
          clearable
          @keydown.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #prefix>
            <n-icon>
              <SearchOutline />
            </n-icon>
          </template>
        </n-input>
      </div>

      <!-- 机构列表 -->
      <div class="org-list-container">
        <n-scrollbar style="max-height: calc(90vh - 200px)">
          <n-list hoverable clickable>
            <n-list-item
              v-for="org in filteredOrgList"
              :key="org.id"
              :class="{ selected: selectedOrgId === org.id }"
              @click="handleOrgSelect(org)"
            >
              <div class="org-item">
                <!-- 机构名称 + 主营品牌标签 -->
                <div class="org-header">
                  <div class="org-name">{{ org.orgName }}</div>
                  <div class="brand-tags" v-if="org.salesBrands">
                    <n-tag
                      v-for="brand in getBrandList(org.salesBrands)"
                      :key="brand.value"
                      size="small"
                      :type="brand.type"
                      :color="brand.color"
                    >
                      {{ brand.label }}
                    </n-tag>
                  </div>
                </div>
              </div>
            </n-list-item>
          </n-list>
        </n-scrollbar>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <n-spin size="medium" />
      </div>

      <!-- 空状态 -->
      <div
        v-if="!loading && filteredOrgList.length === 0"
        class="empty-container"
      >
        <n-empty description="暂无机构数据" />
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import {
  NCard,
  NInput,
  NButton,
  NIcon,
  NSpace,
  NList,
  NListItem,
  NTag,
  NScrollbar,
  NSpin,
  NEmpty,
} from "naive-ui";
import { SearchOutline, RefreshOutline } from "@vicons/ionicons5";
import { getBizOrgList } from "@/api/bizOrg";
import { getDictOptions } from "@/api/dict";
import messages from "@/utils/messages";

// 定义组件事件
const emit = defineEmits(["select-org"]);

// 组件状态
const loading = ref(false);
const searchKeyword = ref("");
const orgList = ref([]);
const selectedOrgId = ref(null);

// 字典数据
const brandDict = ref({});

// 计算属性：过滤后的机构列表
const filteredOrgList = computed(() => {
  if (!searchKeyword.value) {
    return orgList.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return orgList.value.filter((org) =>
    org.orgName.toLowerCase().includes(keyword)
  );
});

// 获取品牌列表
const getBrandList = (salesBrands) => {
  if (!salesBrands) return [];

  // 自定义品牌颜色配置
  const brandColorConfig = {
    AVATR: { color: "#8B5CF6", textColor: "#FFFFFF" }, // 阿维塔 - 紫红色
    DEEPAL: { color: "#1E40AF", textColor: "#FFFFFF" }, // 深蓝 - 深蓝色
    GRAVITY: { color: "#10B981", textColor: "#FFFFFF" }, // 引力 - 绿色
    QIYUAN: { color: "#6B7280", textColor: "#FFFFFF" }, // 启源 - 灰色
    KAICHENG: { color: "#F59E0B", textColor: "#FFFFFF" }, // 凯程 - 黄色
    CHANGAN: { color: "#18a058", textColor: "#FFFFFF" }, // 长安汽车 - 保持原绿色
  };

  const brands = salesBrands
    .split(",")
    .map((brand) => brand.trim())
    .filter((brand) => brand);

  return brands.map((brandValue) => {
    const brandInfo = brandDict.value[brandValue];
    const customColor = brandColorConfig[brandValue];

    return {
      value: brandValue,
      label: brandInfo?.label || brandValue,
      type: undefined, // 不使用预设类型，使用自定义颜色
      color: customColor
        ? {
            color: customColor.color,
            textColor: customColor.textColor,
            borderColor: customColor.color,
          }
        : brandInfo?.color
        ? { color: brandInfo.color }
        : undefined,
    };
  });
};

// 获取品牌字典数据
const fetchBrandDict = async () => {
  try {
    const brandRes = await getDictOptions("vehicle_brand");
    if (brandRes.code === 200) {
      const brandMap = {};
      brandRes.data.forEach((item) => {
        brandMap[item.option_value] = {
          label: item.option_label,
          type: item.type || "default",
          color: item.color,
        };
      });
      brandDict.value = brandMap;
    }
  } catch (error) {
    console.error("获取品牌字典数据失败:", error);
  }
};

// 获取业务机构列表
const fetchOrgList = async () => {
  loading.value = true;
  try {
    const response = await getBizOrgList({});

    if (response.code === 200) {
      orgList.value = response.data.list || response.data || [];

      // 如果有数据且没有选中的机构，默认选中第一个
      if (orgList.value.length > 0 && !selectedOrgId.value) {
        const firstOrg = orgList.value[0];
        handleOrgSelect(firstOrg);
      }
    } else {
      messages.error(response.message || "获取机构列表失败");
    }
  } catch (error) {
    console.error("获取机构列表失败:", error);
    messages.error("获取机构列表失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理机构选择
const handleOrgSelect = (org) => {
  selectedOrgId.value = org.id;
  emit("select-org", org.id, org);
};

// 处理搜索
const handleSearch = () => {
  // 搜索是通过计算属性实现的，这里不需要额外操作
};

// 刷新数据
const refreshData = () => {
  fetchOrgList();
};

// 组件挂载时获取数据
onMounted(async () => {
  await fetchBrandDict();
  await fetchOrgList();
});

// 暴露方法给父组件
defineExpose({
  refreshData,
  getCurrentSelectedOrgId: () => selectedOrgId.value,
});
</script>

<style lang="scss" scoped>
.biz-org-list-selector {
  height: 100%;

  .org-list-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.n-card__content) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }
  }

  .search-area {
    margin-bottom: 16px;
  }

  .org-list-container {
    flex: 1;
    min-height: 0;
  }

  .org-item {
    width: 100%;

    .org-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;

      .org-name {
        font-weight: 500;
        color: var(--n-text-color);
        flex: 1;
        margin-right: 12px;
      }

      .brand-tags {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
        flex-shrink: 0;
      }
    }
  }

  :deep(.n-list-item) {
    &.selected {
      background-color: rgba(24, 160, 88, 0.1);
      border-left: 3px solid #18a058;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}
</style>
