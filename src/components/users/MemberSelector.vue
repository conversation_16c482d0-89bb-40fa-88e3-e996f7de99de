<template>
    <div class="member-selector" :class="{ 'is-disabled': disabled, 'single-mode': mode === 'single' }"
         :style="{ width }" @click="handleClick" ref="containerRef">
        <div class="selected-members" :class="{ 'has-selected': hasSelectedMembers }">
            <template v-if="hasSelectedMembers">
                <div class="selected-tags" :class="{ 'single-mode': mode === 'single' }">
                    <template v-if="mode === 'single'">
                        <n-tag :key="normalizedValue.id" size="medium" round type="success"
                            class="member-tag" :closable="true" @close.stop="removeMember(normalizedValue)">
                            <template #avatar>
                                <n-icon><User /></n-icon>
                            </template>
                            {{ normalizedValue.name }}
                        </n-tag>
                    </template>
                    <template v-else>
                        <template v-if="normalizedValue.length <= 5">
                            <n-tag v-for="member in normalizedValue" :key="member.id" size="medium" round type="success"
                                class="member-tag" :closable="true" @close.stop="removeMember(member)">
                                <template #avatar>
                                    <n-icon><User /></n-icon>
                                </template>
                                {{ member.name }}
                            </n-tag>
                        </template>
                        <template v-else>
                            <n-tag v-for="member in normalizedValue.slice(0, 5)" :key="member.id" size="medium" round
                                type="success" class="member-tag" :closable="true" @close.stop="removeMember(member)">
                                <template #avatar>
                                    <n-icon><User /></n-icon>
                                </template>
                                {{ member.name }}
                            </n-tag>
                            <n-tag size="medium" round type="info" class="member-tag more-tag" closable
                                @close.stop="removeMember('all')">
                                等{{ normalizedValue.length }}人
                            </n-tag>
                        </template>
                    </template>
                </div>
            </template>
            <template v-else>
                <n-icon size="24">
                    <component :is="mode === 'multiple' ? Users : User" />
                </n-icon>
                <span class="selector-text">{{ label }}</span>
            </template>
        </div>

        <n-modal v-model:show="visible" style="width: 80vw; max-width: 1200px;" :mask-closable="false" preset="card"
            @close="hidePanel">
            <template #header>
                <div class="panel-title">{{ label }}</div>
            </template>

            <div class="modal-content">
                <div class="selected-member-container">
                    <template v-if="tempSelectedMembers.length > 0">
                        <div class="selected-members-list">
                            <template v-if="tempSelectedMembers.length > 5">
                                <n-tag v-for="member in tempSelectedMembers.slice(0, 5)" :key="member.key" size="medium"
                                    round closable class="member-tag" @close="clearTempSelection(member.key)">
                                    <template #avatar>
                                        <n-icon>
                                            <User />
                                        </n-icon>
                                    </template>
                                    {{ member.label }}
                                </n-tag>
                                <n-tag size="medium" round type="info" class="member-tag more-tag" closable
                                    @close.stop="removeMember('all')">
                                    等{{ tempSelectedMembers.length }}人
                                </n-tag>
                            </template>
                            <template v-else>
                                <n-tag v-for="member in tempSelectedMembers" :key="member.key" size="medium" round
                                    closable class="member-tag" @close="clearTempSelection(member.key)">
                                    <template #avatar>
                                        <n-icon>
                                            <User />
                                        </n-icon>
                                    </template>
                                    {{ member.label }}
                                </n-tag>
                            </template>
                        </div>
                    </template>
                    <div v-else class="no-selection">未选择成员</div>
                </div>

                <n-input v-model:value="searchText" placeholder="搜索成员" clearable>
                    <template #prefix>
                        <n-icon>
                            <Search />
                        </n-icon>
                    </template>
                </n-input>

                <div class="panel-content">
                    <div class="org-tree">
                        <n-tree block-line :data="treeData" :expanded-keys="expandedKeys"
                            @update:expanded-keys="handleExpand" :selected-keys="selectedDeptKeys"
                            @update:selected-keys="handleDeptSelect" :loading="loading"
                            :render-switcher-icon="renderSwitcherIcon" selectable key-field="key" label-field="label"
                            children-field="children" />
                    </div>

                    <div class="member-list">
                        <n-spin :show="loading">
                            <div v-if="mode === 'multiple'" class="select-all-area">
                                <n-checkbox :checked="isAllSelected" :indeterminate="isIndeterminate"
                                    @update:checked="handleSelectAll">
                                    全选
                                </n-checkbox>
                            </div>

                            <div class="member-list-content">
                                <div v-for="member in filteredMembers" :key="member.key" class="member-item"
                                    :class="{ 'member-item--selected': tempSelectedMembers.some(m => m.key === member.key) }"
                                    @click="handleMemberSelect(member)">
                                    <div class="member-info">
                                        <n-checkbox v-if="mode === 'multiple'" v-model:checked="member.checked"
                                            @click.stop />
                                        <n-radio v-else :checked="tempSelectedMembers.some(m => m.key === member.key)"
                                            @click.stop />
                                        <n-icon>
                                            <User />
                                        </n-icon>
                                        <span class="member-name">{{ member.label }}</span>
                                    </div>
                                </div>
                            </div>
                        </n-spin>
                    </div>
                </div>
            </div>

            <template #footer>
                <n-space justify="end">
                    <n-button @click="hidePanel">取消</n-button>
                    <n-button type="primary" :disabled="!tempSelectedMembers.length" @click="confirmSelection">
                        确定
                    </n-button>
                </n-space>
            </template>
        </n-modal>
    </div>
</template>

<script setup>
import { ref, computed, h, watch, onMounted } from 'vue'
import { NInput, NButton, NSpace, NTree, NIcon, NSpin, NCheckbox, NRadio, NTag } from 'naive-ui'
import { Search, User, Users } from '@vicons/tabler'
import { MinusSquareOutlined, PlusSquareOutlined } from '@vicons/antd'
import { getDepartments, getDepartmentMembers } from '@/api/users'

const props = defineProps({
    modelValue: {
        type: [Object, Array, null],
        default: null
    },
    mode: {
        type: String,
        default: 'single',
        validator: (value) => ['single', 'multiple'].includes(value)
    },
    disabled: {
        type: Boolean,
        default: false
    },
    label: {
        type: String,
        default: '选择成员'
    },
    width: {
        type: String,
        default: '100%'
    },
    deptId: {
        type: [Number, String, null],
        default: null,
        description: '指定部门ID，如果提供则只显示该部门'
    },
    defaultExpanded: {
        type: Boolean,
        default: false,
        description: '是否默认展开所有节点'
    }
})

const emit = defineEmits(['update:modelValue'])

// 组件状态
const containerRef = ref(null)
const visible = ref(false)
const searchText = ref('')
const selectedMemberIds = ref([])
const expandedKeys = ref([])
const loading = ref(false)
const treeData = ref([])
const currentDeptMembers = ref([])
const selectedDeptKeys = ref([])
const tempSelectedMembers = ref([])

// 计算属性
const defaultValue = computed(() => {
    return props.mode === 'single' ? null : []
})

const normalizedValue = computed({
    get() {
        return props.modelValue ?? defaultValue.value
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

const hasSelectedMembers = computed(() => {
    const value = normalizedValue.value
    if (!value) return false
    return Array.isArray(value) ? value.length > 0 : true
})

// 添加 filteredMembers 计算属性
const filteredMembers = computed(() => {
    if (!searchText.value) {
        return currentDeptMembers.value
    }

    const searchLower = searchText.value.toLowerCase()
    return currentDeptMembers.value.filter(member =>
        member.label.toLowerCase().includes(searchLower)
    )
})

// 添加全局搜索相关状态
const searchingAll = ref(false)

// 修改全选相关的计算属性
const isAllSelected = computed(() => {
    return filteredMembers.value.length > 0 &&
        filteredMembers.value.every(member =>
            tempSelectedMembers.value.some(m => m.key === member.key)
        )
})

const isIndeterminate = computed(() => {
    if (filteredMembers.value.length === 0) return false

    const selectedCount = filteredMembers.value.filter(member =>
        tempSelectedMembers.value.some(m => m.key === member.key)
    ).length

    return selectedCount > 0 && selectedCount < filteredMembers.value.length
})

// 方法
const buildTreeData = (departments) => {
    const options = []
    const map = {}

    // 第一步：创建所有节点的映射
    departments.forEach(dept => {
        map[dept.id] = {
            key: dept.id.toString(),
            label: dept.name,
            children: [],
            isLeaf: true,
            rawData: dept // 保存原始数据
        }
    })

    // 第二步：构建树形结构
    departments.forEach(dept => {
        if (dept.parentId === 0 || !dept.parentId) {
            // 根节点直接添加到结果中
            options.push(map[dept.id])
        } else {
            // 子节点添加到其父节点下
            const parent = map[dept.parentId]
            if (parent) {
                parent.children.push(map[dept.id])
                parent.isLeaf = false // 如果有子节点，则不是叶子节点
            } else {
                // 如果找不到父节点，将其作为根节点处理
                console.warn(`Parent node with ID ${dept.parentId} not found for department ${dept.name} (ID: ${dept.id}). Treating as root node.`)
                options.push(map[dept.id])
            }
        }
    })

    return options
}

const loadDepartments = async () => {
    try {
        loading.value = true
        const response = await getDepartments()
        if (response.code === 200) {
            // 构建完整的部门树
            treeData.value = buildTreeData(response.data)

            if (treeData.value.length > 0) {
                // 获取所有节点的key用于展开
                const getAllKeys = (nodes) => {
                    const keys = []
                    const traverse = (nodeList) => {
                        nodeList.forEach(node => {
                            keys.push(node.key)
                            if (node.children && node.children.length > 0) {
                                traverse(node.children)
                            }
                        })
                    }
                    traverse(nodes)
                    return keys
                }

                // 根据 defaultExpanded 属性决定是否展开所有节点
                if (props.defaultExpanded) {
                    // 展开所有节点
                    expandedKeys.value = getAllKeys(treeData.value)
                } else {
                    // 只展开根节点
                    expandedKeys.value = treeData.value.map(node => node.key)
                }

                // 如果指定了部门ID，则选中该部门，否则选中第一个根节点
                if (props.deptId) {
                    const deptIdStr = String(props.deptId)
                    selectedDeptKeys.value = [deptIdStr]
                    await loadDepartmentMembers(deptIdStr)
                } else {
                    selectedDeptKeys.value = [treeData.value[0].key]
                    await loadDepartmentMembers(treeData.value[0].key)
                }
            }
        }
    } catch (error) {
        console.error('Failed to load departments:', error)
    } finally {
        loading.value = false
    }
}

const handleDeptSelect = async (keys) => {
    if (!keys || keys.length === 0) return
    const key = keys[0]
    selectedDeptKeys.value = [key]
    await loadDepartmentMembers(key)
}

const renderSwitcherIcon = ({ expanded, isLeaf }) => {
    if (isLeaf) return null
    return expanded ? h(MinusSquareOutlined) : h(PlusSquareOutlined)
}

const transformMemberData = (members) => {
    return members.map(member => ({
        key: member.id.toString(),
        label: member.nickname,
        workMobile: member.workMobile,
        type: 'user',
        checked: false
    }))
}

const loadDepartmentMembers = async (deptId) => {
    try {
        loading.value = true
        const response = await getDepartmentMembers(deptId)
        if (response.code === 200) {
            const members = transformMemberData(response.data)
            // 同步选中状态
            members.forEach(member => {
                member.checked = selectedMemberIds.value.includes(String(member.key))
            })
            currentDeptMembers.value = members
        } else {
            currentDeptMembers.value = []
        }
    } catch (error) {
        console.error('Failed to load department members:', error)
        currentDeptMembers.value = []
    } finally {
        loading.value = false
    }
}

const handleMemberSelect = (member) => {
    if (props.mode === 'single') {
        tempSelectedMembers.value = [{
            key: String(member.key),
            label: member.label,
            type: 'user'
        }]
        selectedMemberIds.value = [String(member.key)]

        // 单选模式下直接确认选择
        confirmSelection()
    } else {
        member.checked = !member.checked
        const index = tempSelectedMembers.value.findIndex(m => m.key === String(member.key))

        if (index === -1) {
            tempSelectedMembers.value.push({
                key: String(member.key),
                label: member.label,
                type: 'user'
            })
            selectedMemberIds.value.push(String(member.key))
        } else {
            tempSelectedMembers.value.splice(index, 1)
            selectedMemberIds.value = selectedMemberIds.value.filter(id => id !== String(member.key))
        }
    }
}

const confirmSelection = () => {
    const selected = tempSelectedMembers.value.map(member => ({
        id: member.key,
        name: member.label
    }))

    if (props.mode === 'single') {
        emit('update:modelValue', selected[0] || null)
    } else {
        emit('update:modelValue', selected)
    }
    hidePanel()
}

const clearTempSelection = (memberKey) => {
    const index = tempSelectedMembers.value.findIndex(m => m.key === String(memberKey))
    if (index !== -1) {
        tempSelectedMembers.value.splice(index, 1)
        selectedMemberIds.value = selectedMemberIds.value.filter(id => id !== String(memberKey))

        // 更新成员的选中状态
        const member = currentDeptMembers.value.find(m => m.key === String(memberKey))
        if (member) {
            member.checked = false
        }
    }
}

const handleExpand = (keys) => {
    expandedKeys.value = keys
}

const handleClick = () => {
    if (!props.disabled) {
        visible.value = true
        initializeSelection()

        // 首次打开时加载数据
        if (!treeData.value.length) {
            loadDepartments()
        } else if (props.deptId && selectedDeptKeys.value.length === 0) {
            // 如果提供了deptId但尚未选择部门，则选择该部门
            const deptIdStr = String(props.deptId)
            selectedDeptKeys.value = [deptIdStr]
            loadDepartmentMembers(deptIdStr)
        }
    }
}

const hidePanel = () => {
    visible.value = false
    tempSelectedMembers.value = []
    selectedMemberIds.value = []
    searchText.value = ''
}

// 添加全局搜索法

// 修改全选处理方法
const handleSelectAll = (checked) => {
    console.log('handleSelectAll', checked)

    if (checked) {
        // 全选：将当前过滤列表中的所有成员添加到选中列表
        const newSelected = [...tempSelectedMembers.value] // 创建数组保留原有选择

        filteredMembers.value.forEach(member => {
            // 如果成员还未被选中，则添加到选中列表
            if (!newSelected.some(m => m.key === member.key)) {
                newSelected.push({
                    key: member.key,
                    label: member.label,
                    type: 'user'
                })
            }
            // 更新复选框状态
            member.checked = true
        })

        // 一次性更新选中列表
        tempSelectedMembers.value = newSelected
        selectedMemberIds.value = newSelected.map(m => m.key)
    } else {
        // 取消全选：从选中列中移除当前滤列表中的所有成员
        const filterMemberKeys = new Set(filteredMembers.value.map(m => m.key))

        // 更新复选框状态
        filteredMembers.value.forEach(member => {
            member.checked = false
        })

        // 移除当前列表中的成员，保留其他已选成员
        tempSelectedMembers.value = tempSelectedMembers.value.filter(
            member => !filterMemberKeys.has(member.key)
        )
        selectedMemberIds.value = tempSelectedMembers.value.map(m => m.key)
    }
}

// 修改 clearAllSelected 方法
const clearAllSelected = () => {
    // 清除临时选择
    tempSelectedMembers.value = []
    selectedMemberIds.value = []

    // 清除所有成员的复选框状态
    currentDeptMembers.value.forEach(member => {
        member.checked = false
    })

    // 清除实际选择
    emit('update:modelValue', props.mode === 'single' ? null : [])
}

// 修改 removeMember 方法
const removeMember = (member) => {
    if (member === 'all') {
        clearAllSelected()
    } else {
        const newValue = props.mode === 'single'
            ? null
            : normalizedValue.value.filter(m => String(m.id) !== String(member.id))
        emit('update:modelValue', newValue)
    }
}

// 添加初始化选择的方法
const initializeSelection = () => {
    const currentSelection = props.modelValue
    if (!currentSelection) {
        tempSelectedMembers.value = []
        selectedMemberIds.value = []
        return
    }

    // 处理单选和多选情况
    const selections = Array.isArray(currentSelection) ? currentSelection : [currentSelection]

    tempSelectedMembers.value = selections.map(member => ({
        key: String(member.id),
        label: member.name,
        type: 'user'
    }))

    selectedMemberIds.value = selections.map(member => String(member.id))

    // 更新当前列表中成员的选中状态
    currentDeptMembers.value.forEach(member => {
        member.checked = selectedMemberIds.value.includes(String(member.key))
    })
}

// 修改 watch modelValue 的逻辑
watch(() => props.modelValue, (newValue) => {
    initializeSelection()
}, { immediate: true, deep: true })

// 添加 mounted 钩子来检查初始值
onMounted(() => {
    console.log('Initial modelValue:', props.modelValue)
    if (props.modelValue) {
        initializeSelection()
    }
})

// 监听 deptId 变化
watch(() => props.deptId, (newDeptId) => {
    console.log('MemberSelector deptId changed:', newDeptId)
    // 如果弹窗已打开，则重新加载部门树
    if (visible.value) {
        loadDepartments()
    } else {
        // 清空已加载的数据，以便下次打开时重新加载
        treeData.value = []
    }
}, { immediate: false })
</script>

<style scoped>
.member-selector {
    min-width: 120px;
    max-width: 100%;
    position: relative;
}

.selected-members {
    border: 2px dashed var(--primary-color);
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: rgba(var(--primary-color-rgb), 0.02);
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.selected-members:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.selected-members.is-disabled {
    cursor: not-allowed;
    opacity: 0.6;
    background-color: #f5f5f5;
    border-color: #ddd;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    padding: 4px;
    min-height: 32px;
    align-items: center;
}

.selector-text {
    color: #666;
    font-size: 14px;
}

.n-icon {
    color: #999;
}

.member-tag {
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.member-tag:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.member-tag :deep(.n-tag__content) {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.member-tag :deep(.n-tag__close) {
    margin-left: 4px;
    transition: all 0.3s ease;
}

.member-tag:hover :deep(.n-tag__close) {
    background-color: rgba(0, 0, 0, 0.1);
}

.modal-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 70vh;
    max-height: 800px;
}

.panel-content {
    flex: 1;
    min-height: 0;
    display: flex;
    gap: 16px;
}

.org-tree {
    width: 70%;
    border-right: 1px solid #eee;
    overflow: auto;
    padding-right: 16px;
}

.member-list {
    width: 30%;
    flex: none;
    overflow: auto;
    padding-left: 8px;
}

.member-item {
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    user-select: none;
}

.member-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.member-item--selected {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.member-item--selected .member-name {
    color: var(--primary-color);
    font-weight: 500;
}

.member-info {
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: none;
}

.member-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.selected-member-container {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    min-height: 48px;
}

.selected-members-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.no-selection {
    color: #999;
    font-size: 14px;
    padding: 8px;
}

.no-members {
    text-align: center;
    color: #999;
    padding: 20px 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 添加单选框样式 */
:deep(.n-radio) {
    margin-right: 4px;
}

:deep(.n-radio__input) {
    cursor: pointer;
}

/* 添加单选模式特定样式 */
.selected-tags.single-mode {
    justify-content: center;
    /* 水平居中 */
    flex-wrap: nowrap;
    /* 防止换行 */
}

.selected-tags.single-mode .member-tag {
    margin: 0;
    /* 移除可能的外边距 */
}

.more-tag {
    background-color: #f5f5f5 !important;
    border: 1px solid #e8e8e8;
    color: #666;
    cursor: default;
}

.more-tag:hover {
    background-color: #f0f0f0 !important;
}

.more-tag :deep(.n-tag__close) {
    color: #999;
    transition: all 0.3s ease;
}

.more-tag:hover :deep(.n-tag__close) {
    color: #666;
    background-color: rgba(0, 0, 0, 0.1);
}

.select-all-area {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    background-color: #fafafa;
    position: sticky;
    top: 0;
    z-index: 1;
}

.member-list-content {
    padding-top: 8px;
}

.member-selector.single-mode .selected-members {
    min-height: 32px;
    padding: 6px 10px;
}

.selected-members.has-selected {
    justify-content: flex-start;
    padding: 6px 10px;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
}

.member-tag {
    margin: 0;
}
</style>