<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    title="订单详情"
    preset="card"
    style="width: 100%; height: 100%;"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-spin :show="loading">
      <div v-if="orderDetail" class="order-detail-content">
        <!-- 基本信息 -->
        <div class="section-title">
          <span class="title-text">基本信息</span>
        </div>
        <n-divider title-placement="left"></n-divider>
        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="订单编号">
              <div class="detail-item">
                <span class="order-sn">{{ orderDetail.orderSn }}</span>
                <n-button quaternary circle size="small" @click="copyToClipboard(orderDetail.orderSn)">
                  <template #icon>
                    <n-icon color="#18a058"><component :is="CopyOutlineIcon" /></n-icon>
                  </template>
                </n-button>
              </div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="订单日期">
              <div class="detail-item">{{ formatDate(orderDetail.dealDate) }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="订单状态">
              <div class="detail-item">
                <n-tag :type="getOrderStatusType(orderDetail.orderStatus)" size="small">
                  {{ getOrderStatusText(orderDetail.orderStatus) }}
                </n-tag>
              </div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="支付方式">
              <div class="detail-item">{{ orderDetail.paymentMethod === 'FULL' ? '全款' : '分期' }}</div>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 客户信息 -->
        <div class="section-title">
          <span class="title-text">客户信息</span>
        </div>
        <n-divider title-placement="left"></n-divider>
        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="客户名称">
              <div class="detail-item">{{ orderDetail.customerName }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="联系电话">
              <div class="detail-item">{{ orderDetail.mobile || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="客户地址">
              <div class="detail-item">{{ orderDetail.customerAddress || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="客户邮箱">
              <div class="detail-item">{{ orderDetail.customerEmail || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 销售信息 -->
        <div class="section-title">
          <span class="title-text">销售信息</span>
        </div>
        <n-divider title-placement="left"></n-divider>
        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="销售单位">
              <div class="detail-item">{{ orderDetail.salesOrgName }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售顾问">
              <div class="detail-item">{{ orderDetail.salesAgentName }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售主管">
              <div class="detail-item">{{ orderDetail.salesLeaderName }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="交付单位">
              <div class="detail-item">{{ orderDetail.deliveryOrgName }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="交付日期">
              <div class="detail-item">{{ formatDate(orderDetail.deliveryDate) }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="经办人">
              <div class="detail-item">{{ orderDetail.creatorName }}</div>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 车辆信息 -->
        <div class="section-title">
          <span class="title-text">车辆信息</span>
        </div>
        <n-divider title-placement="left"></n-divider>
        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="品牌">
              <div class="detail-item">{{ orderDetail.brand }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="配置">
              <div class="detail-item">{{ orderDetail.configName }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车系">
              <div class="detail-item">{{ orderDetail.series || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="颜色">
              <div class="detail-item">{{ orderDetail.color || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="年款">
              <div class="detail-item">{{ orderDetail.year || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="排量">
              <div class="detail-item">{{ orderDetail.displacement || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="发动机类型">
              <div class="detail-item">{{ orderDetail.engineType || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="变速箱类型">
              <div class="detail-item">{{ orderDetail.transmissionType || '未设置' }}</div>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 金额信息 -->
        <div class="section-title">
          <span class="title-text">金额信息</span>
        </div>
        <n-divider title-placement="left"></n-divider>
        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="成交价格">
              <div class="detail-item money">¥{{ formatMoney(orderDetail.dealAmount) }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="成交价格(大写)">
              <div class="detail-item">{{ orderDetail.dealAmountCn }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售价格">
              <div class="detail-item money">¥{{ formatMoney(orderDetail.salesAmount) }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="优惠金额">
              <div class="detail-item money">¥{{ formatMoney(orderDetail.discountAmount) }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售费用">
              <div class="detail-item money">¥{{ formatMoney(orderDetail.salesCostAmount) }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="毛利润">
              <div class="detail-item" :class="orderDetail.grossProfitAmount > 0 ? 'profit' : 'negative-profit'">
                ¥{{ formatMoney(orderDetail.grossProfitAmount) }}
              </div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="毛利率">
              <div class="detail-item" :class="orderDetail.grossProfitRate > 0 ? 'profit' : 'negative-profit'">
                {{ orderDetail.grossProfitRate }}%
              </div>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 贷款信息 -->
        <template v-if="orderDetail.paymentMethod === 'LOAN'">
          <div class="section-title">
            <span class="title-text">贷款信息</span>
          </div>
          <n-divider title-placement="left"></n-divider>
          <n-grid :cols="4" :x-gap="16" :y-gap="16">
            <n-grid-item>
              <n-form-item label="贷款渠道">
                <div class="detail-item">{{ orderDetail.loanChannel || '未设置' }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="贷款金额">
                <div class="detail-item money">¥{{ formatMoney(orderDetail.loanAmount) }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="首付金额">
                <div class="detail-item money">¥{{ formatMoney(orderDetail.loanInitialAmount) }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="首付比例">
                <div class="detail-item">{{ (orderDetail.loanInitialRatio * 100).toFixed(2) }}%</div>
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </template>

        <!-- 二手车置换信息 -->
        <template v-if="orderDetail.usedVehicleId">
          <div class="section-title">
            <span class="title-text">二手车置换信息</span>
          </div>
          <n-divider title-placement="left"></n-divider>
          <n-grid :cols="4" :x-gap="16" :y-gap="16">
            <n-grid-item>
              <n-form-item label="二手车车牌号">
                <div class="detail-item">{{ orderDetail.usedVehicleId }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="二手车VIN码">
                <div class="detail-item">{{ orderDetail.usedVehicleVin }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="二手车品牌">
                <div class="detail-item">{{ orderDetail.usedVehicleBound || '未设置' }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="二手车车型">
                <div class="detail-item">{{ orderDetail.usedVehicleModel || '未设置' }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="二手车颜色">
                <div class="detail-item">{{ orderDetail.usedVehicleColor || '未设置' }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="置换金额">
                <div class="detail-item money">¥{{ formatMoney(orderDetail.usedVehicleAmount) }}</div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="置换补贴">
                <div class="detail-item money">¥{{ formatMoney(orderDetail.usedVehicleDiscountAmount) }}</div>
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </template>

        <!-- 时间信息 -->
        <div class="section-title">
          <span class="title-text">其他信息</span>
        </div>
        <n-divider title-placement="left"></n-divider>
        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="创建时间">
              <div class="detail-item">{{ orderDetail.createTime }}</div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="更新时间">
              <div class="detail-item">{{ orderDetail.updateTime }}</div>
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </div>
      <div v-else class="empty-data">
        <n-empty description="暂无订单数据" />
      </div>
    </n-spin>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, markRaw } from 'vue'
import {
  NModal, NSpin, NButton, NSpace, NEmpty, NIcon, NTag,
  NDivider, NGrid, NGridItem, NFormItem
} from 'naive-ui'
import { CopyOutline } from '@vicons/ionicons5'
import vehicleOrderApi from '@/api/vehicleOrder'
import messages from '@/utils/messages'

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const CopyOutlineIcon = markRaw(CopyOutline)

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  id: {
    type: [Number, String, null],
    default: null
  }
})

// 定义组件事件
const emit = defineEmits(['update:visible'])

// 组件状态
const loading = ref(false)
const orderDetail = ref(null)

// 计算属性：模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 更新可见性
const updateVisible = (val) => {
  emit('update:visible', val)
}

// 监听visible和id属性变化
watch([() => props.visible, () => props.id], ([newVisible, newId]) => {
  if (newVisible && newId) {
    fetchOrderDetail(newId)
  }
})

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未设置';
  // 如果是时间戳，转换为日期字符串
  if (typeof dateStr === 'number') {
    return new Date(dateStr).toLocaleDateString('zh-CN');
  }
  // 如果是日期字符串，提取日期部分
  return dateStr.split(' ')[0];
}

// 格式化金额（分转元，并添加千分位分隔符）
const formatMoney = (amount) => {
  if (amount === undefined || amount === null) return '0.00';
  // 将分转换为元
  const yuan = amount / 100;
  // 格式化为千分位格式，保留两位小数
  return yuan.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

// 获取订单详情
const fetchOrderDetail = async (id) => {
  if (!id) return

  loading.value = true
  try {
    const response = await vehicleOrderApi.getOrderDetail(id)
    if (response.code === 200) {
      // 直接使用API返回的数据
      orderDetail.value = response.data;
    } else {
      messages.error(response.message || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    messages.error('获取订单详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 复制到剪贴板
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    messages.success('复制成功')
  }).catch(() => {
    messages.error('复制失败')
  })
}

// 获取订单状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'processing': 'info',
    'completed': 'success',
    'cancelled': 'error'
  }
  return statusMap[status] || 'default'
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

// 处理关闭
const handleClose = () => {
  modelVisible.value = false
}
</script>

<style scoped>
.order-detail-content {
  padding: 0 10px;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

:deep(.n-divider) {
  margin-top: 8px;
  margin-bottom: 16px;
  background-image: linear-gradient(to right, var(--primary-color) 0%, rgba(0, 0, 0, 0.06) 100%);
}

.detail-item {
  min-height: 32px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
}

/* 金额相关字段突出显示 */
.detail-item.money {
  color: #2080f0;
  font-weight: 600;
}

/* 利润相关字段突出显示 */
.detail-item.profit {
  color: #18a058;
  font-weight: 600;
}

/* 负利润显示红色 */
.detail-item.negative-profit {
  color: #d03050;
  font-weight: 600;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

/* 订单编号特殊样式 */
.order-sn {
  font-family: monospace;
  letter-spacing: 1px;
  font-weight: 600;
}

/* 状态标签增强样式 */
:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
