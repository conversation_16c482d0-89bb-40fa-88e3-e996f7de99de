<template>
  <div>
    <div class="section-title">
      <span class="title-text">车辆置换</span>
    </div>
    <n-divider title-placement="left"></n-divider>

    <div class="option-row">
      <span class="option-label">客户是否有车辆置换？</span>
      <n-radio-group v-model:value="form.hasUsedVehicle">
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span class="option-tip" v-if="form.hasUsedVehicle === 'YES'">
        请填写二手车置换信息，车牌号和VIN码为必填项。
      </span>
    </div>

    <!-- 当选择"有"时显示二手车置换信息 -->
    <n-grid v-if="form.hasUsedVehicle === 'YES'" :cols="4" :x-gap="16" :y-gap="1">
      <n-grid-item>
        <n-form-item label="置换车牌号" path="usedVehicleId" required>
          <n-input v-model:value="form.usedVehicleId" placeholder="请输入置换车牌号" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车VIN" path="usedVehicleVin" required>
          <n-input v-model:value="form.usedVehicleVin" placeholder="请输入17位车辆VIN" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车品牌" path="usedVehicleBound">
          <n-input v-model:value="form.usedVehicleBound" placeholder="请输入置换车品牌" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车型" path="usedVehicleModel">
          <n-input v-model:value="form.usedVehicleModel" placeholder="请输入置换车型" />
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item label="置换车颜色" path="usedVehicleColor">
          <n-input v-model:value="form.usedVehicleColor" placeholder="请输入置换车颜色" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换金额(元)" path="usedVehicleAmount">
          <div style="display: flex; align-items: center; width: 100%;">
            <n-input-number v-model:value="form.usedVehicleAmount" placeholder="请输入置换金额" style="flex: 1;"
              :precision="2" :min="0" button-placement="both" />
            <n-checkbox v-model:checked="form.usedVehicleDeductible" style="margin-left: 10px; white-space: nowrap; width: 80px;">转车款</n-checkbox>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换补贴(元)" path="usedVehicleDiscountAmount">
          <div style="display: flex; align-items: center; width: 100%;">
            <n-input-number v-model:value="form.usedVehicleDiscountAmount" placeholder="请输入置换补贴" style="flex: 1;"
              :precision="2" :min="0" button-placement="both" />
            <n-checkbox v-model:checked="form.usedVehicleDiscountDeductible" style="margin-left: 10px; white-space: nowrap; width: 80px;">转车款</n-checkbox>
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})
</script>
