<template>
  <div>
    <div class="section-title">
      <span class="title-text">订单备注</span>
    </div>
    <n-divider title-placement="left"></n-divider>

    <n-grid :cols="4" :x-gap="16" :y-gap="1">
      <n-grid-item span="2">
        <n-form-item label="其他未尽事宜，请备注" path="paymentRemark">
          <n-input v-model:value="form.paymentRemark" type="textarea" placeholder="备注信息将传递给后续节点处理" :autosize="{ minRows: 3, maxRows: 6 }" />
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})
</script>
