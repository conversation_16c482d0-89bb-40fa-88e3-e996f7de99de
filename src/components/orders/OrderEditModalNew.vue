<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    :title="title"
    preset="card"
    style="width: 100%; height: 100%"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
      class="order-form"
    >
      <!-- 客户信息部分 -->
      <customer-info-section
        :form="form"
        @show-customer-selector="showCustomerSelector"
      />

      <!-- 产品信息部分 -->
      <product-info-section
        :form="form"
        :selected-outbound-org="selectedOutboundOrg"
        @show-vehicle-selector="showVehicleSelector"
        @handle-outbound-org-change="handleOutboundOrgChange"
        @handle-sale-price-change="handleSalePriceChange"
        @handle-discount-change="handleDiscountChange"
        @handle-sales-expense-change="handleSalesExpenseChange"
      />

      <!-- 付款方式部分 -->
      <payment-method-section
        :form="form"
        :loan-channel-options="loanChannelOptions"
        :loan-months-options="loanMonthsOptions"
        @handle-loan-amount-change="handleLoanAmountChange"
        @handle-loan-initial-amount-change="handleLoanInitialAmountChange"
        @handle-loan-fee-change="handleLoanFeeChange"
      />

      <!-- 车辆置换部分 -->
      <vehicle-exchange-section :form="form" />

      <!-- 车辆保险部分 -->
      <insurance-section :form="form" />

      <!-- 售前衍生收入部分 -->
      <derivative-income-section :form="form" />

      <!-- 赠品明细部分 -->
      <gift-items-section :form="form" ref="giftItemsSectionRef" />

      <!-- 订单备注部分 -->
      <remark-section :form="form" />

      <!-- 财务结算部分 -->
      <financial-settlement-section
        :form="form"
        @handle-sale-price-change="handleSalePriceChange"
        @handle-discount-change="handleDiscountChange"
        @handle-exclusive-discount-change="handleExclusiveDiscountChange"
      />
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave">确定</n-button>
      </n-space>
    </template>

    <!-- 客户选择器 -->
    <customer-selector
      v-model:visible="customerSelectorVisible"
      @select="handleCustomerSelected"
    />

    <!-- 车辆选择器 -->
    <vehicle-s-k-u-selector
      v-model:visible="vehicleSelectorVisible"
      @select="handleVehicleSelected"
    />
  </n-modal>
</template>

<script setup>
import { useOrderEditModal } from "./OrderEditModalNew.js";

// 引入选择器组件
import CustomerSelector from "@/components/customer/CustomerSelector.vue";
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";

// 引入各个部分组件
import CustomerInfoSection from "@/components/orders/sections/CustomerInfoSection.vue";
import ProductInfoSection from "@/components/orders/sections/ProductInfoSection.vue";
import PaymentMethodSection from "@/components/orders/sections/PaymentMethodSection.vue";
import VehicleExchangeSection from "@/components/orders/sections/VehicleExchangeSection.vue";
import InsuranceSection from "@/components/orders/sections/InsuranceSection.vue";
import DerivativeIncomeSection from "@/components/orders/sections/DerivativeIncomeSection.vue";
import GiftItemsSection from "@/components/orders/sections/GiftItemsSection.vue";
import RemarkSection from "@/components/orders/sections/RemarkSection.vue";
import FinancialSettlementSection from "@/components/orders/sections/FinancialSettlementSection.vue";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增订单",
  },
  initialData: {
    type: Object,
    default: () => ({}),
  },
  vehicleCategoryOptions: {
    type: Array,
    default: () => [],
  },
  orderStatusOptions: {
    type: Array,
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "save", "cancel"]);

// 使用组合式函数
const {
  // 响应式数据
  formRef,
  customerSelectorVisible,
  vehicleSelectorVisible,
  giftItemsSectionRef,
  selectedOutboundOrg,
  form,
  rules,
  loanChannelOptions,
  loanMonthsOptions,
  modelVisible,

  // 方法
  updateVisible,
  showCustomerSelector,
  showVehicleSelector,
  handleVehicleSelected,
  handleSalePriceChange,
  handleDiscountChange,
  handleSalesExpenseChange,
  handleExclusiveDiscountChange,
  handleLoanAmountChange,
  handleLoanInitialAmountChange,
  handleLoanFeeChange,
  handleOutboundOrgChange,
  handleCustomerSelected,
  handleSave,
  handleCancel,
  resetForm,
  setFormData,
} = useOrderEditModal(props, emit);

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData,
});
</script>

<style lang="scss">
@use "./OrderEditModalNew.scss";
</style>
