<template>
  <div class="file-upload-button" :style="customStyle">
    <!-- 导入按钮 -->
    <n-button
      :type="buttonType"
      @click="showImportDialog"
      round
      :class="{ 'simple-mode': buttonMode === 'simple' }"
      :style="buttonStyle"
      :title="buttonMode === 'simple' ? buttonText : ''"
    >
      <template #icon>
        <n-icon><CloudUploadOutline /></n-icon>
      </template>
      <span v-if="buttonMode === 'standard'">{{ buttonText }}</span>
    </n-button>

    <!-- 文件上传弹窗 -->
    <n-modal
      v-model:show="importDialogVisible"
      :title="modalTitle"
      preset="card"
      :style="{ width: '500px' }"
      :mask-closable="false"
    >
      <n-upload
        ref="uploadRef"
        :custom-request="customRequest"
        :default-upload="false"
        :max="1"
        :accept="actualAcceptFormats"
        :max-size="maxSizeBytes"
        :show-file-list="true"
        :show-remove-button="true"
        :show-download-button="false"
        :show-retry-button="false"
        :show-preview-button="false"
        :show-cancel-button="false"
        :show-file-list-button="false"
        @change="handleUploadChange"
        @before-upload="handleBeforeUpload"
      >
        <n-upload-dragger>
          <div class="upload-trigger">
            <n-icon size="48" :depth="3">
              <CloudUploadOutline />
            </n-icon>
            <p>点击或拖拽文件到此区域</p>
            <p class="tip">
              {{ acceptFormats === '*' ? '支持所有文件格式' : `支持 ${acceptFormats} 格式` }}，最大 {{ maxSize }}MB
            </p>
          </div>
        </n-upload-dragger>
      </n-upload>
      <n-space vertical v-if="showImportTips">
        <n-alert title="导入说明" type="info" style="margin-top: 16px;">
          <ol class="import-tips">
            <li>请确保文件格式正确，包含必要的内容</li>
            <li>导入前请检查数据的完整性和准确性</li>
            <li v-if="templateUrl">可以<a href="#" @click.prevent="downloadTemplate">下载模板</a>进行填写</li>
          </ol>
        </n-alert>
      </n-space>
      <template #footer>
        <n-space justify="end">
          <n-button @click="importDialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleImport" :disabled="!uploadFiles.length" :loading="uploading">导入</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { CloudUploadOutline } from '@vicons/ionicons5'
import messages from '@/utils/messages'
import { doGet } from '@/utils/requests'
import axios from 'axios'

// 定义组件属性
const props = defineProps({
  // 支持的文件格式，未设置或为*时允许所有类型
  acceptFormats: {
    type: String,
    default: '*'
  },
  // 模板文件下载链接
  templateUrl: {
    type: String,
    default: ''
  },
  // 最大文件大小(MB)
  maxSize: {
    type: Number,
    default: 10
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: '文件上传'
  },
  // 按钮类型
  buttonType: {
    type: String,
    default: 'info'
  },
  // 按钮模式: standard(标准模式，显示图标和文字) 或 simple(简约模式，只显示图标)
  buttonMode: {
    type: String,
    default: 'standard',
    validator: (value) => ['standard', 'simple'].includes(value)
  },
  // 自定义按钮样式
  buttonStyle: {
    type: Object,
    default: () => ({})
  },
  // 自定义组件容器样式
  customStyle: {
    type: Object,
    default: () => ({})
  },

})

// 定义事件
const emit = defineEmits(['success', 'error', 'cancel'])

// 消息提示
// 使用全局消息组件
// const message = useMessage()

// 状态变量
const uploadRef = ref(null)
const importDialogVisible = ref(false)
const uploadFiles = ref([])
const uploading = ref(false)

// 计算属性
// 实际的accept格式
const actualAcceptFormats = computed(() => {
  // 如果是*或者空，则不限制文件类型
  return props.acceptFormats === '*' || !props.acceptFormats ? '' : props.acceptFormats
})

// 模态标题
const modalTitle = computed(() => {
  // 如果是Excel文件上传，显示"Excel导入"，否则显示"文件上传"
  if (props.acceptFormats.includes('.xls') || props.acceptFormats.includes('.xlsx')) {
    return 'Excel导入'
  }
  return '文件上传'
})

// 是否显示导入提示
const showImportTips = computed(() => {
  // 如果有模板链接或者是Excel文件上传，则显示导入提示
  return !!props.templateUrl || props.acceptFormats.includes('.xls') || props.acceptFormats.includes('.xlsx')
})

// 计算最大文件大小（字节）
const maxSizeBytes = computed(() => props.maxSize * 1024 * 1024)

// 显示导入对话框
const showImportDialog = () => {
  uploadFiles.value = []
  importDialogVisible.value = true
}

// 处理上传变化
const handleUploadChange = ({ fileList }) => {
  uploadFiles.value = fileList
}

// 上传前检查
const handleBeforeUpload = ({ file }) => {
  // 如果是*或者空，则不限制文件类型
  if (props.acceptFormats === '*' || !props.acceptFormats) {
    // 跳过文件类型检查
  } else {
    // 检查文件类型
    const fileExt = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
    const acceptedExts = props.acceptFormats.split(',').map(ext => ext.trim().toLowerCase())

    if (!acceptedExts.includes(fileExt)) {
      nextTick(() => {
        messages.error(`不支持的文件格式: ${fileExt}，请上传 ${props.acceptFormats} 格式的文件`)
      })
      return false
    }
  }

  // 检查文件大小
  if (file.size > maxSizeBytes.value) {
    nextTick(() => {
      messages.error(`文件大小超过限制，最大支持 ${props.maxSize}MB`)
    })
    return false
  }

  return true
}

// 自定义上传请求
const customRequest = ({ onFinish }) => {
  // 这里只是暂存文件，不实际上传
  // 实际上传在点击"导入"按钮时进行
  onFinish()
}

// 处理导入
const handleImport = async () => {
  if (!uploadFiles.value.length) {
    nextTick(() => {
      messages.warning('请先上传文件')
    })
    return
  }

  uploading.value = true

  try {
    const file = uploadFiles.value[0].file

    // 生成上传参数获取接口的路径，使用当前日期格式为 yyyyMMdd
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const dateStr = `${year}${month}${day}`
    const uploadParamsUrl = `/system/preUploadParams?open_id=${dateStr}`

    // 获取上传参数
    const paramsResponse = await doGet(uploadParamsUrl)

    // 获取上传参数
    const uploadParams = paramsResponse.data

    // 构建表单数据
    const formData = new FormData()

    // 添加腾讯云COS所需的参数
    if (uploadParams.params) {
      Object.keys(uploadParams.params).forEach(key => {
        formData.append(key, uploadParams.params[key])
      })
    }

    // 添加文件，使用文件字段名 'file'
    formData.append('file', file)

    // 确保上传URL是完整的，包含协议部分
    let uploadUrl = uploadParams.host
    if (uploadUrl && !uploadUrl.startsWith('http')) {
      uploadUrl = `https://${uploadUrl}`
    }

    // 调用直传接口（腾讯云COS）
    const uploadResponse = await axios.post(uploadUrl, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        // 计算上传进度
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        console.log(`上传进度: ${percentCompleted}%`)
      }
    })

    // 处理上传响应
    // 腾讯云COS直传成功后不返回文件路径，需要自己构建
    // 使用host和fileKey构建文件路径
    let filePath = ''

    // 如果上传成功，腾讯云COS返回空响应，状态码为200
    if (uploadResponse.status === 204) {
      // 构建文件访问路径
      filePath = `${uploadParams.fileKey}`
    } else {
      throw new Error('文件上传失败')
    }

    if (filePath) {
      // 使用 nextTick 确保消息在渲染周期内显示
      nextTick(() => {
        //messages.success('文件上传成功')
      })

      // 发射成功事件，将文件路径返回给父组件
      emit('success', {
        filePath: filePath,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        fileKey: uploadParams.fileKey, // 返回文件在存储中的唯一标识
        uploadResponse: uploadResponse
      })

      // 关闭弹窗
      importDialogVisible.value = false
    } else {
      throw new Error('无法获取上传文件路径')
    }
  } catch (error) {
    console.error('上传失败:', error)
    nextTick(() => {
      messages.error(error.message || '文件上传失败，请重试')
    })

    // 发射错误事件
    emit('error', error.message || '文件上传失败')
  } finally {
    uploading.value = false
  }
}

// 下载模板
const downloadTemplate = () => {
  if (!props.templateUrl) {
    nextTick(() => {
      messages.warning('未提供模板下载链接')
    })
    return
  }

  // 创建一个隐藏的a标签来下载文件
  const link = document.createElement('a')
  link.href = props.templateUrl
  link.target = '_blank'
  link.download = 'template.xlsx' // 可以根据实际情况修改文件名
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>

<style scoped>
.file-upload-button {
  display: inline-block;
}

/* 简约模式下的按钮样式 */
.file-upload-button .n-button.simple-mode {
  min-width: 0;
  padding: 0 8px;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.upload-trigger p {
  margin: 8px 0 0;
}

.upload-trigger .tip {
  font-size: 12px;
  color: #999;
}

.import-tips {
  padding-left: 20px;
  margin: 8px 0;
}

.import-tips li {
  margin-bottom: 6px;
}

.import-tips a {
  color: var(--primary-color);
  text-decoration: none;
}

.import-tips a:hover {
  text-decoration: underline;
}
</style>
