/* 业务机构列表布局组件样式 */

.biz-org-list-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 主卡片样式
  .main-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    :deep(.n-card__content) {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
  }

  // 数据列表卡片样式
  .data-list-card {
    flex: 1;
    display: flex;
    flex-direction: column;

    :deep(.n-card__content) {
      padding: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
  }

  // 表格容器样式 - 强制高度设置
  .table-container {
    min-height: calc(100vh - 200px) !important;
  }

  // 分页容器样式 - 固定在底部
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 8px 15px;
    background-color: #fafafa;
    border-top: 1px solid #e0e0e6;
    min-height: 40px;
    flex-shrink: 0;
    overflow-x: auto;
  }

  // 虚拟滚动表头样式
  .n-data-table-base-table-header {
    background-color: #fafafa;
    border-bottom: 1px solid #e0e0e6;
  }

  .n-data-table-base-table-header .n-data-table-th {
    background-color: #fafafa !important;
    text-align: center !important;
    font-weight: 600;
  }

  .n-data-table-base-table-header .n-data-table-th__title {
    justify-content: center;
  }
}

// 分页组件内部样式优化
:deep(.pagination-container .n-pagination) {
  flex-wrap: nowrap;
  white-space: nowrap;
}

// 隐藏表格容器的滚动条
:deep(.data-table-wrapper::-webkit-scrollbar) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

// 加载状态样式
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: var(--n-text-color-disabled);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
  }
}