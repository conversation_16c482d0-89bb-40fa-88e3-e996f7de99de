<template>
  <n-modal
    v-model:show="modalVisible"
    title="启票详情"
    preset="card"
    :style="isMaximized ? { width: '95%', height: '95%' } : { width: '720px' }"
    :mask-closable="true"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component :is="isMaximized ? ContractOutline : ExpandOutline" />
          </n-icon>
        </template>
      </n-button>
    </template>

    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>

    <div v-else-if="error" class="error-container">
      <n-result status="error" :title="error" />
    </div>

    <n-descriptions v-else bordered :column="2" label-placement="left">
      <n-descriptions-item label="订单日期">
        {{ detailData?.erpOrderDate || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="订单类型">
        {{ detailData?.orderType || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="订单编号">
        <div style="display: flex; align-items: center;">
          <span>{{ detailData?.erpOrderNo || '-' }}</span>
          <n-button v-if="detailData?.erpOrderNo" quaternary circle size="small" @click="copyToClipboard(detailData.erpOrderNo)" style="margin-left: 5px;" title="复制订单编号">
            <template #icon>
              <n-icon color="#18a058">
                <CopyOutline />
              </n-icon>
            </template>
          </n-button>
        </div>
      </n-descriptions-item>
      <n-descriptions-item label="启票单位">
        {{ detailData?.invoiceOrgName || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="车辆类别">
        {{ detailData?.vehicleCategory || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="车型系列">
        {{ detailData?.vehicleSeries || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="车型代码">
        {{ detailData?.vehicleModelCode || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="车型名称">
        {{ detailData?.vehicleModelName || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="VIN">
        <div style="display: flex; align-items: center;">
          <span>{{ detailData?.vin || '-' }}</span>
          <n-button v-if="detailData?.vin" quaternary circle size="small" @click="copyToClipboard(detailData.vin)" style="margin-left: 5px;" title="复制VIN">
            <template #icon>
              <n-icon color="#18a058">
                <CopyOutline />
              </n-icon>
            </template>
          </n-button>
          <n-button v-if="detailData?.vin" quaternary circle size="small" @click="showVehicleTracking(detailData.vin)" style="margin-left: 5px;" title="查看车辆流程">
            <template #icon>
              <n-icon color="#2080f0">
                <OpenOutline />
              </n-icon>
            </template>
          </n-button>
        </div>
      </n-descriptions-item>
      <n-descriptions-item label="启票金额">
        <span style="font-weight: bold; color: #f0a020;">
          {{ detailData?.startBillPrice != null ? `¥${Number(detailData.startBillPrice).toFixed(2)}` : '-' }}
        </span>
      </n-descriptions-item>
      <n-descriptions-item label="省份">
        {{ detailData?.province || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="城市">
        {{ detailData?.city || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="付款备注">
        {{ detailData?.paymentRemark || '-' }}
      </n-descriptions-item>

      <n-descriptions-item label="资金类型">
        {{ detailData?.fundType || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="采购单位">
        {{ detailData?.purchaseOrgName || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="收车单位">
        {{ detailData?.receivingOrgName || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="发运地址">
        {{ detailData?.shippingAddress || '-' }}
      </n-descriptions-item>
      <n-descriptions-item label="发货申请编号" >
        <div style="display: flex; align-items: center;">
          <span>{{ detailData?.shippingApplicationNo || '-' }}</span>
          <n-button v-if="detailData?.shippingApplicationNo" quaternary circle size="small" @click="copyToClipboard(detailData.shippingApplicationNo)" style="margin-left: 5px;" title="复制发货申请编号">
            <template #icon>
              <n-icon color="#18a058">
                <CopyOutline />
              </n-icon>
            </template>
          </n-button>
        </div>
      </n-descriptions-item>
      <n-descriptions-item label="发货申请日期">
        {{ detailData?.shippingApplicationDate || '-' }}
      </n-descriptions-item>
    </n-descriptions>

    <template #footer>
      <n-space justify="end">
        <n-button @click="closeModal">关闭</n-button>
      </n-space>
    </template>
  </n-modal>

  <!-- 车辆流程追踪弹窗 -->
  <VehicleTrackingModal
    v-model:visible="showTrackingModal"
    :vin="currentVin"
  />
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ContractOutline, ExpandOutline, CopyOutline, OpenOutline } from '@vicons/ionicons5'
import startBillApi from '@/api/startBill'
import messages from '@/utils/messages'
import VehicleTrackingModal from './VehicleTrackingModal.vue'

const props = defineProps({
  id: {
    type: [Number, String],
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

// 状态变量
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})
const isMaximized = ref(true)
const loading = ref(false)
const error = ref(null)
const detailData = ref(null)
const showTrackingModal = ref(false)
const currentVin = ref('')

// 切换弹窗最大化/最小化
const toggleSize = () => {
  isMaximized.value = !isMaximized.value
}

// 关闭弹窗
const closeModal = () => {
  modalVisible.value = false
}

// 复制到剪贴板
const copyToClipboard = (text) => {
  if (!text) return

  navigator.clipboard.writeText(text)
    .then(() => {
      messages.success('复制成功')
    })
    .catch(err => {
      console.error('复制失败:', err)
      messages.error('复制失败')
    })
}

// 显示车辆流程追踪信息
const showVehicleTracking = (vin) => {
  if (!vin) return

  currentVin.value = vin
  showTrackingModal.value = true
}

// 加载详情数据
const loadDetailData = async () => {
  if (!props.id) return

  loading.value = true
  error.value = null

  try {
    const response = await startBillApi.getStartBillDetail(props.id)

    if (response.code === 200) {
      detailData.value = response.data
    } else {
      error.value = response.message || '获取详情失败'
      messages.error(error.value)
    }
  } catch (err) {
    console.error('获取启票详情失败:', err)
    error.value = '获取详情失败，请稍后重试'
    messages.error(error.value)
  } finally {
    loading.value = false
  }
}

// 监听 visible 和 id 变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.id) {
    loadDetailData()
  }
})

watch(() => props.id, (newVal) => {
  if (modalVisible.value && newVal) {
    loadDetailData()
  }
})
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.error-container {
  min-height: 200px;
}
</style>
