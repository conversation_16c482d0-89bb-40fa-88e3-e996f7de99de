<template>
  <n-modal
    :show="visible"
    @update:show="handleVisibleChange"
    preset="card"
    :title="isEdit ? '编辑车型' : '新增车型'"
    :style="isMaximized ? { width: '90%', height: '90%' } : { width: '600px' }"
    :mask-closable="false"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component :is="isMaximized ? ContractOutline : ExpandOutline" />
          </n-icon>
        </template>
      </n-button>
    </template>

    <div class="edit-content">
      <n-spin :show="loading">
        <n-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
        >
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="品牌" path="brand">
                <n-input v-model:value="form.brand" placeholder="请输入车辆品牌" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="车系" path="series">
                <n-input v-model:value="form.series" placeholder="请输入车型系列" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="车型代码" path="modelCode">
                <n-input v-model:value="form.modelCode" placeholder="请输入车型代码" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="车型名称" path="modelName">
                <n-input v-model:value="form.modelName" placeholder="请输入车型名称" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="配置代码" path="configCode">
                <n-input v-model:value="form.configCode" placeholder="请输入配置代码" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="配置名称" path="configName">
                <n-input v-model:value="form.configName" placeholder="请输入配置名称" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="启票价格(元)" path="sbPrice">
                <n-input-number v-model:value="form.sbPrice" placeholder="请输入启票价格" style="width: 100%" :precision="0" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="颜色代码" path="colorCode">
                <n-input v-model:value="form.colorCode" placeholder="请输入颜色代码" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="SKU_ID" path="skuId">
                <n-input 
                  v-model:value="form.skuId" 
                  placeholder="留空将自动生成" 
                  :disabled="isEdit"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <!-- 预留位置 -->
            </n-grid-item>
          </n-grid>
          <n-form-item label="付款备注" path="paymentRemark">
            <n-input v-model:value="form.paymentRemark" type="textarea" placeholder="请输入付款备注信息" />
          </n-form-item>
        </n-form>
      </n-spin>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">取消</n-button>
        <n-button type="primary" :loading="submitting" @click="handleSubmit">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, watch, defineComponent, computed } from 'vue'
import { NModal, NButton, NIcon, NSpin, NForm, NFormItem, NInput, NInputNumber, NGrid, NGridItem, NSpace } from 'naive-ui'
import { ContractOutline, ExpandOutline } from '@vicons/ionicons5'
import skuApi from '@/api/sku'
import messages from '@/utils/messages'

// 显式声明组件，消除IDE警告
defineComponent({
  components: {
    NModal, NButton, NIcon, NSpin, NForm, NFormItem, NInput, NInputNumber, NGrid, NGridItem, NSpace,
    ContractOutline, ExpandOutline
  }
})

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  id: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 状态变量
const formRef = ref(null)
const loading = ref(false)
const submitting = ref(false)
const isMaximized = ref(true)
const isEdit = computed(() => !!props.id)

// 表单数据
const form = ref({
  brand: '',
  series: '',
  modelCode: '',
  modelName: '',
  configCode: '',
  configName: '',
  sbPrice: null,
  colorCode: '',
  skuId: '',
  paymentRemark: ''
})

// 表单验证规则
const rules = {
  brand: {
    required: true,
    message: '请输入车辆品牌',
    trigger: ['blur', 'input']
  },
  series: {
    required: true,
    message: '请输入车型系列',
    trigger: ['blur', 'input']
  },
  modelCode: {
    required: true,
    message: '请输入车型代码',
    trigger: ['blur', 'input']
  },
  modelName: {
    required: true,
    message: '请输入车型名称',
    trigger: ['blur', 'input']
  },
  configCode: {
    required: true,
    message: '请输入配置代码',
    trigger: ['blur', 'input']
  },
  configName: {
    required: true,
    message: '请输入配置名称',
    trigger: ['blur', 'input']
  },
  sbPrice: {
    required: true,
    message: '请输入启票价格',
    trigger: ['blur', 'change']
  },
  colorCode: {
    required: true,
    message: '请输入颜色代码',
    trigger: ['blur', 'input']
  }
}

// 切换弹窗大小
const toggleSize = () => {
  isMaximized.value = !isMaximized.value
}

// 处理弹窗显示状态变化
const handleVisibleChange = (value) => {
  emit('update:visible', value)
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 获取详情数据
const fetchDetail = async (id) => {
  if (!id) {
    resetForm()
    return
  }

  loading.value = true
  try {
    const response = await skuApi.getSkuDetail(id)
    if (response.code === 200) {
      const data = response.data
      // 填充表单数据
      form.value = {
        id: data.id,
        brand: data.brand || '',
        series: data.series || '',
        modelCode: data.modelCode || '',
        modelName: data.modelName || '',
        configCode: data.configCode || '',
        configName: data.configName || '',
        sbPrice: data.sbPrice,
        colorCode: data.colorCode || '',
        skuId: data.skuId || '',
        paymentRemark: data.paymentRemark || ''
      }
    } else {
      messages.error(response.message || '获取详情失败')
      resetForm()
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    messages.error('获取详情失败，请稍后重试')
    resetForm()
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    brand: '',
    series: '',
    modelCode: '',
    modelName: '',
    configCode: '',
    configName: '',
    sbPrice: null,
    colorCode: '',
    skuId: '',
    paymentRemark: ''
  }
  formRef.value?.restoreValidation()
}

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return

    submitting.value = true
    try {
      // 准备请求数据
      const data = { ...form.value }
      
      // 如果是编辑模式，添加ID
      if (isEdit.value) {
        data.id = props.id
      }
      
      // 如果SKU_ID为空，自动生成
      if (!data.skuId) {
        data.skuId = `${data.modelCode}.${data.configCode}.${data.colorCode}`
      }

      // 调用保存API
      const response = isEdit.value
        ? await skuApi.updateSku(data)
        : await skuApi.addSku(data)

      if (response.code === 200) {
        messages.success(isEdit.value ? '更新成功' : '添加成功')
        emit('success')
        handleClose()
      } else {
        messages.error(response.message || (isEdit.value ? '更新失败' : '添加失败'))
      }
    } catch (error) {
      console.error('保存失败:', error)
      messages.error('保存失败，请稍后重试')
    } finally {
      submitting.value = false
    }
  })
}

// 监听ID变化，获取详情
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      fetchDetail(newId)
    } else {
      resetForm()
    }
  }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      if (props.id) {
        fetchDetail(props.id)
      } else {
        resetForm()
      }
    }
  }
)
</script>

<style scoped>
.edit-content {
  min-height: 300px;
  padding: 8px 0;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}
</style>
