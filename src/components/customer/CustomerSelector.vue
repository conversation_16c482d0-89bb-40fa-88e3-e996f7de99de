<template>
  <n-modal
    :show="modelVisible"
    @update:show="updateVisible"
    :title="title || '我的客户'"
    preset="card"
    :style="{ width: '800px', maxWidth: '90%' }"
    :mask-closable="false"
    :auto-focus="false"
    class="customer-selector-modal"
  >
    <div class="customer-selector-content">
      <!-- 搜索区域 -->
      <div class="search-area">
        <n-input
          v-model:value="searchKeyword"
          placeholder="请输入客户名称或手机号码"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><component :is="SearchOutlineIcon" /></n-icon>
          </template>
        </n-input>
        <n-button type="primary" @click="handleSearch" class="search-button">
          搜索
        </n-button>
      </div>

      <!-- 数据列表 -->
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="customerData"
        :loading="loading"
        :pagination="pagination"
        :row-key="row => row.id"
        :row-props="rowProps"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :disabled="!selectedCustomer" @click="handleConfirm">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, h, markRaw, watch } from 'vue'
import { NModal, NInput, NButton, NSpace, NDataTable, NIcon, NTag } from 'naive-ui'
import { SearchOutline } from '@vicons/ionicons5'
import customerApi from '@/api/customer'
import messages from '@/utils/messages'

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const SearchOutlineIcon = markRaw(SearchOutline)

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '我的客户'
  },
  // 可选的初始选中客户
  initialCustomer: {
    type: Object,
    default: null
  }
})

// 定义组件事件
const emit = defineEmits(['update:visible', 'select', 'cancel'])

// 组件状态
const loading = ref(false)
const searchKeyword = ref('')
const customerData = ref([])
const selectedCustomerId = ref(null)
const selectedCustomer = ref(null)
const tableRef = ref(null)

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0,
  onChange: (page) => {
    pagination.page = page
    fetchCustomers()
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    fetchCustomers()
  }
})

// 表格列配置
const columns = [
  {
    type: 'selection',
    disabled: () => false,
    multiple: false,
    selectedRowKeys: computed(() => selectedCustomerId.value ? [selectedCustomerId.value] : []),
    onUpdateChecked: (checked, row) => {
      if (checked) {
        selectedCustomerId.value = row.id
        selectedCustomer.value = row
      } else {
        selectedCustomerId.value = null
        selectedCustomer.value = null
      }
    }
  },
  { title: '客户名称', key: 'customerName', width: 150 },
  {
    title: '客户类型',
    key: 'customerType',
    width: 100,
    render(row) {
      const statusMap = {
        'individual': { text: '个人客户', type: 'info', color: '#2080f0' },
        'institutional': { text: '法人客户', type: 'success', color: '#18a058' },
      }

      const status = statusMap[row.customerType] || { text: '未知', type: 'default', color: '#909399' }

      return h(
        NTag,
        {
          type: status.type,
          bordered: false,
          style: {
            padding: '2px 8px',
            fontWeight: 'bold'
          }
        },
        { default: () => status.text }
      )
    }
  },
  { title: '手机号码', key: 'mobile', width: 120 },
  { title: '所属单位', key: 'ownerOrgName', width: 180 },
  { title: '销售顾问', key: 'ownerSellerName', width: 100 }
]

// 计算属性：模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 更新可见性
const updateVisible = (val) => {
  emit('update:visible', val)
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时，重置搜索条件并加载数据
    searchKeyword.value = ''
    pagination.page = 1
    fetchCustomers()

    // 如果有初始选中客户，则设置选中状态
    if (props.initialCustomer) {
      selectedCustomerId.value = props.initialCustomer.id
      selectedCustomer.value = props.initialCustomer
    } else {
      selectedCustomerId.value = null
      selectedCustomer.value = null
    }
  }
})

// 获取客户列表
const fetchCustomers = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize
    }

    // 添加搜索关键字
    if (searchKeyword.value) {
      params.keywords = searchKeyword.value
    }

    // 调用API获取数据
    const response = await customerApi.getCustomerList(params)

    if (response.code === 200) {
      customerData.value = response.data.list

      // 更新分页信息
      pagination.itemCount = response.data.total
      pagination.pageCount = response.data.pages
    } else {
      messages.error(response.message || '获取客户列表失败')
    }
  } catch (error) {
    console.error('获取客户列表失败:', error)
    messages.error('获取客户列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  fetchCustomers()
}

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page
  fetchCustomers()
}

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchCustomers()
}

// 处理取消
const handleCancel = () => {
  modelVisible.value = false
  emit('cancel')
}

// 处理确认
const handleConfirm = () => {
  if (selectedCustomer.value) {
    emit('select', selectedCustomer.value)
    modelVisible.value = false
  }
}

// 行属性函数 - 添加双击事件和选中样式
const rowProps = (row) => {
  return {
    style: selectedCustomerId.value === row.id ? 'background-color: rgba(24, 160, 88, 0.1);' : '',
    onClick: () => {
      // 单击选中行
      selectedCustomerId.value = row.id
      selectedCustomer.value = row
    },
    onDblclick: () => {
      // 双击选中并确认
      selectedCustomerId.value = row.id
      selectedCustomer.value = row
      handleConfirm()
    }
  }
}
</script>

<style scoped>
.customer-selector-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-area {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.search-button {
  flex-shrink: 0;
}

:deep(.n-data-table .n-data-table-td) {
  padding: 8px 12px;
}

:deep(.n-tag) {
  margin: 0;
}

/* 选中行样式 */
:deep(.n-data-table-tr.n-data-table-tr--selected) {
  background-color: rgba(24, 160, 88, 0.1) !important;
}

/* 鼠标悬停样式 */
:deep(.n-data-table-tr:hover) {
  cursor: pointer;
  background-color: rgba(24, 160, 88, 0.05);
}
</style>
