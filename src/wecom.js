import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import naive from 'naive-ui'
import App from '@/App.vue'
import WecomLoginPage from '@/views/system/WecomLoginPage.vue'
import LoginPage from '@/views/system/LoginPage.vue'
import NotFoundPage from '@/views/system/NotFoundPage.vue'
import { createPinia } from 'pinia'
import MainPage from '@/views/MainPage.vue'
const pinia = createPinia()

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: MainPage },
    { path: '/login', component: LoginPage },
    { path: '/wecom', component: WecomLoginPage },
    { path: '/:pathMatch(.*)*', component: NotFoundPage }
  ]
})

const app = createApp(App)

app.use(router)
app.use(naive)
app.use(pinia)
app.mount('#app')
