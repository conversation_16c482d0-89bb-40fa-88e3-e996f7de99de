/**
 * 业务字典模拟数据
 */

// 字典列表
export const dictList = [
  {
    dict_code: 'vehicle_brand',
    dict_name: '在售品牌',
    remark: '长安旗下在售品牌'
  },
  {
    dict_code: 'receivable_subject',
    dict_name: '应收科目',
    remark: '财务应收账款科目'
  },
  {
    dict_code: 'receivable_target',
    dict_name: '应收对象',
    remark: '财务应收账款对象'
  },
  {
    dict_code: 'recept_account',
    dict_name: '收款方式',
    remark: '财务收款方式'
  },
  {
    dict_code: 'loan_channel',
    dict_name: '贷款渠道',
    remark: '分期贷款渠道'
  },
  {
    dict_code: 'loan_months',
    dict_name: '贷款期限',
    remark: '分期贷款期限'
  },
  {
    dict_code: 'exclusive_discount_type',
    dict_name: '专享优惠类型',
    remark: '客户专享优惠类型'
  },
  {
    dict_code: 'deposit_status',
    dict_name: '定金状态',
    remark: '定金订单状态'
  },
  {
    dict_code: 'province_city',
    dict_name: '省/直辖市',
    remark: '业务组织架构省份'
  },
  {
    dict_code: 'city_district',
    dict_name: '市/区',
    remark: '业务组织架构城市区域'
  },
  {
    dict_code: 'stock_status',
    dict_name: '仓储状态',
    remark: '库存商品的仓储状态'
  },
  {
    dict_code: 'business_permission',
    dict_name: '业务权限',
    remark: '业务组织架构权限类型'
  },
  {
    dict_code: 'org_type',
    dict_name: '机构类型',
    remark: '业务组织架构类型'
  },
  {
    dict_code: 'order_status',
    dict_name: '订单状态',
    remark: '销售订单状态'
  }
]

// 字典选项
export const dictOptions = {
  'gift_category': [
    {
      option_value: 'GOODS',
      option_label: '商品',
      sort: 1,
      remark: '实物赠品'
    },
    {
      option_value: 'SERVICES',
      option_label: '服务',
      sort: 2,
      remark: '虚拟赠品'
    }
  ],
  'vehicle_brand': [
    {
      option_value: 'DEEPAL',
      option_label: '深蓝',
      sort: 2,
      remark: '深蓝汽车',
      type: 'info',
      color: '#2080f0'
    },
    {
      option_value: 'KAICHENG',
      option_label: '凯程',
      sort: 3,
      remark: '凯程汽车',
      type: 'warning',
      color: '#f0a020'
    },
    {
      option_value: 'GRAVITY',
      option_label: '引力',
      sort: 4,
      remark: '引力汽车',
      type: 'default',
      color: '#909399'
    },
    {
      option_value: 'AVATR',
      option_label: '阿维塔',
      sort: 5,
      remark: '阿维塔汽车',
      type: 'error',
      color: '#d03050'
    },
    {
      option_value: 'QIYUAN',
      option_label: '启源',
      sort: 6,
      remark: '启源汽车',
      type: 'default',
      color: '#909399'
    }
  ],

  'vehicle_status': [
    {
      option_value: '1',
      option_label: '在库',
      sort: 1,
      remark: '车辆在库',
      type: 'success',
      color: '#18a058'
    },
    {
      option_value: '2',
      option_label: '预售',
      sort: 2,
      remark: '车辆预售',
      type: 'warning',
      color: '#f0a020'
    },
    {
      option_value: '3',
      option_label: '已售',
      sort: 3,
      remark: '车辆已售',
      type: 'info',
      color: '#2080f0'
    },
    {
      option_value: '4',
      option_label: '维修中',
      sort: 4,
      remark: '车辆维修中',
      type: 'error',
      color: '#d03050'
    }
  ],

  'user_permissions': [
    {
      option_value: 'read',
      option_label: '读取',
      sort: 1,
      remark: '读取权限',
      type: 'info',
      color: '#2080f0'
    },
    {
      option_value: 'write',
      option_label: '写入',
      sort: 2,
      remark: '写入权限',
      type: 'warning',
      color: '#f0a020'
    },
    {
      option_value: 'delete',
      option_label: '删除',
      sort: 3,
      remark: '删除权限',
      type: 'error',
      color: '#d03050'
    },
    {
      option_value: 'admin',
      option_label: '管理',
      sort: 4,
      remark: '管理权限',
      type: 'success',
      color: '#18a058'
    }
  ],
  'recept_account': [
    {
      option_value: 'cash',
      option_label: '现金',
      sort: 1,
      remark: '现金支付'
    },
    {
      option_value: 'citic_8835',
      option_label: '中信银行(8835)',
      sort: 2,
      remark: '中信银行账户8835'
    },
    {
      option_value: 'qrcode',
      option_label: '公户二维码',
      sort: 3,
      remark: '公司账户二维码支付'
    },
    {
      option_value: 'pos',
      option_label: 'POS机刷卡',
      sort: 4,
      remark: 'POS机刷卡支付'
    },
    {
      option_value: 'loan',
      option_label: '贷款',
      sort: 5,
      remark: '银行贷款'
    }
  ],
  'payment_account': [
    {
      option_value: 'cash',
      option_label: '现金',
      sort: 1,
      remark: '现金支付'
    },
    {
      option_value: 'citic_8835',
      option_label: '中信银行(8835)',
      sort: 2,
      remark: '中信银行账户8835'
    },
    {
      option_value: 'pos',
      option_label: 'POS机刷卡',
      sort: 4,
      remark: 'POS机刷卡支付'
    }
  ],
  'receivable_subject': [
    {
      option_value: 1001,
      option_label: '车款-定金',
      sort: 1,
      remark: '购车定金'
    },
    {
      option_value: 1002,
      option_label: '车款-尾款',
      sort: 1,
      remark: '购车尾款'
    },
    {
      option_value: 1007,
      option_label: '车款-首付款',
      sort: 2,
      remark: '购车首付款'
    },
    {
      option_value: 1008,
      option_label: '车款-分期款',
      sort: 3,
      remark: '分期银行贷款'
    },
    {
      option_value: 1006,
      option_label: '返利-银行分期返利',
      sort: 4,
      remark: '银行返利'
    },
    {
      option_value: 1003,
      option_label: '分期-金融服务费',
      sort: 5,
      remark: '金融服务费'
    },
    {
      option_value: 1004,
      option_label: '保险-交强险保费',
      sort: 6,
      remark: '车辆保险费'
    },
    {
      option_value: 1005,
      option_label: '其他衍生收入',
      sort: 7,
      remark: '其他费用'
    }
  ],
  'payable_subject': [
    {
      option_value: 1001,
      option_label: '车款-启票款',
      sort: 1,
      remark: '启票款'
    },
    {
      option_value: 1002,
      option_label: '车款-尾款',
      sort: 1,
      remark: '售车尾款'
    },
    {
      option_value: 1007,
      option_label: '车款-退置换',
      sort: 2,
      remark: '退置换'
    },
    {
      option_value: 1008,
      option_label: '二手车置换补贴',
      sort: 3,
      remark: '置换补贴'
    },
    {
      option_value: 1006,
      option_label: '返利-银行分期返利',
      sort: 4,
      remark: '银行返利'
    },
    {
      option_value: 1003,
      option_label: '分期-金融服务成本',
      sort: 5,
      remark: '金融服务费'
    },
    {
      option_value: 1004,
      option_label: '保险-交强险保费',
      sort: 6,
      remark: '车辆保险费'
    },
    {
      option_value: 1005,
      option_label: '其他衍生成本',
      sort: 7,
      remark: '其他费用'
    }
  ],

  'receivable_target': [
    {
      option_value: '客户',
      option_label: '客户',
      sort: 1,
      remark: '客户'
    },
    {
      option_value: '银行',
      option_label: '银行',
      sort: 2,
      remark: '银行'
    },
    {
      option_value: '保险公司',
      option_label: '保险公司',
      sort: 3,
      remark: '保险公司'
    },
    {
      option_value: '经销商',
      option_label: '经销商',
      sort: 4,
      remark: '经销商'
    },
    {
      option_value: '其他',
      option_label: '其他',
      sort: 5,
      remark: '其他'
    }
  ],


  'payable_target': [
    {
      option_value: '客户',
      option_label: '客户',
      sort: 1,
      remark: '客户'
    },
    {
      option_value: '银行',
      option_label: '银行',
      sort: 2,
      remark: '银行'
    },
    {
      option_value: '保险公司',
      option_label: '保险公司',
      sort: 3,
      remark: '保险公司'
    },
    {
      option_value: '经销商',
      option_label: '经销商',
      sort: 4,
      remark: '经销商'
    },
    {
      option_value: '其他',
      option_label: '其他',
      sort: 5,
      remark: '其他'
    }
  ],

  'loan_channel': [
    {
      option_value: '车贷通担保',
      option_label: '车贷通担保',
      sort: 1,
      remark: '车贷通担保'
    },
    {
      option_value: '建行',
      option_label: '建行',
      sort: 2,
      remark: '建设银行'
    },
    {
      option_value: '长安金融',
      option_label: '长安金融',
      sort: 3,
      remark: '长安金融'
    },
    {
      option_value: '上汽金融',
      option_label: '上汽金融',
      sort: 4,
      remark: '上汽金融'
    },
    {
      option_value: '其他',
      option_label: '其他',
      sort: 5,
      remark: '其他金融机构'
    }
  ],

  'loan_months': [
    {
      option_value: 3,
      option_label: '3期',
      sort: 1,
      remark: '3个月'
    },
    {
      option_value: 6,
      option_label: '6期',
      sort: 2,
      remark: '6个月'
    },
    {
      option_value: 12,
      option_label: '12期',
      sort: 3,
      remark: '12个月'
    },
    {
      option_value: 24,
      option_label: '24期',
      sort: 4,
      remark: '24个月'
    },
    {
      option_value: 36,
      option_label: '36期',
      sort: 5,
      remark: '36个月'
    },
    {
      option_value: 60,
      option_label: '60期',
      sort: 6,
      remark: '60个月'
    }
  ],

  'exclusive_discount_type': [
    {
      option_value: 'GOVERNMENT',
      option_label: '政企优惠',
      sort: 2,
      remark: '政企客户专享优惠'
    },
    {
      option_value: 'WORK_CAR',
      option_label: '工作车优惠',
      sort: 3,
      remark: '工作车专享优惠'
    },
    {
      option_value: 'INTERNAL',
      option_label: '内购优惠',
      sort: 4,
      remark: '员工内购专享优惠'
    },
    {
      option_value: 'TEST_DRIVE',
      option_label: '试乘试驾车优惠',
      sort: 5,
      remark: '试乘试驾车专享优惠'
    },
    {
      option_value: 'OTHERS',
      option_label: '其他请备注',
      sort: 5,
      remark: '其他请填写订单备注'
    }
  ],

  'deposit_status': [
    {
      option_value: 'received',
      option_label: '已收定金',
      sort: 1,
      remark: '已收到客户定金'
    },
    {
      option_value: 'refunded',
      option_label: '已退定金',
      sort: 2,
      remark: '已退还客户定金'
    },
    {
      option_value: 'transferred',
      option_label: '已转车款',
      sort: 3,
      remark: '定金已转入车款'
    }
  ],

  'province_city': [
    {
      option_value: 'shandong',
      option_label: '山东省',
      sort: 1,
      remark: '山东省'
    },
    {
      option_value: 'hebei',
      option_label: '河北省',
      sort: 2,
      remark: '河北省'
    },
    {
      option_value: 'henan',
      option_label: '河南省',
      sort: 3,
      remark: '河南省'
    },
    {
      option_value: 'chongqing',
      option_label: '重庆市',
      sort: 4,
      remark: '重庆市'
    }
  ],

  'city_district': [
    // 山东省城市
    {
      option_value: 'jinan',
      option_label: '济南市',
      sort: 1,
      remark: '山东省济南市',
      parent: 'shandong'
    },
    {
      option_value: 'dezhou',
      option_label: '德州市',
      sort: 2,
      remark: '山东省德州市',
      parent: 'shandong'
    },
    {
      option_value: 'zibo',
      option_label: '淄博市',
      sort: 3,
      remark: '山东省淄博市',
      parent: 'shandong'
    },
    {
      option_value: 'linyi',
      option_label: '临沂市',
      sort: 4,
      remark: '山东省临沂市',
      parent: 'shandong'
    },
    {
      option_value: 'liaocheng',
      option_label: '聊城市',
      sort: 5,
      remark: '山东省聊城市',
      parent: 'shandong'
    },
    {
      option_value: 'dongying',
      option_label: '东营市',
      sort: 6,
      remark: '山东省东营市',
      parent: 'shandong'
    },
    {
      option_value: 'binzhou',
      option_label: '滨州市',
      sort: 7,
      remark: '山东省滨州市',
      parent: 'shandong'
    },
    // 河南省城市
    {
      option_value: 'zhengzhou',
      option_label: '郑州市',
      sort: 8,
      remark: '河南省郑州市',
      parent: 'henan'
    },
    {
      option_value: 'luoyang',
      option_label: '洛阳市',
      sort: 9,
      remark: '河南省洛阳市',
      parent: 'henan'
    },
    {
      option_value: 'zhumadian',
      option_label: '驻马店市',
      sort: 10,
      remark: '河南省驻马店市',
      parent: 'henan'
    },
    // 河北省城市
    {
      option_value: 'shijiazhuang',
      option_label: '石家庄市',
      sort: 11,
      remark: '河北省石家庄市',
      parent: 'hebei'
    },
    {
      option_value: 'cangzhou',
      option_label: '沧州市',
      sort: 12,
      remark: '河北省沧州市',
      parent: 'hebei'
    },
    {
      option_value: 'langfang',
      option_label: '廊坊市',
      sort: 13,
      remark: '河北省廊坊市',
      parent: 'hebei'
    },
    // 重庆市区县
    {
      option_value: 'yuzhong',
      option_label: '渝中区',
      sort: 14,
      remark: '重庆市渝中区',
      parent: 'chongqing'
    },
    {
      option_value: 'jiangbei',
      option_label: '江北区',
      sort: 15,
      remark: '重庆市江北区',
      parent: 'chongqing'
    }
  ],

  'business_permission': [
    {
      option_value: 'can_stock_in',
      option_label: '可入库',
      sort: 1,
      remark: '允许库存入库操作'
    },
    {
      option_value: 'can_sell',
      option_label: '可销售',
      sort: 2,
      remark: '允许销售操作'
    },
    {
      option_value: 'can_stock_out',
      option_label: '可出库',
      sort: 3,
      remark: '允许库存出库操作'
    },
    {
      option_value: 'can_settle',
      option_label: '可结算',
      sort: 4,
      remark: '允许财务结算操作'
    }
  ],

  'org_type': [
    {
      option_value: 'group',
      option_label: '集团',
      sort: 1,
      remark: '集团总部'
    },
    {
      option_value: 'single_store',
      option_label: '单店',
      sort: 2,
      remark: '单一门店'
    },
    {
      option_value: 'secondary_network',
      option_label: '二网',
      sort: 3,
      remark: '二级网络'
    }
  ],

  'stock_status': [
    {
      option_value: 'transiting',
      option_label: '在途',
      sort: 1,
      remark: '货物运输中',
      type: 'info',
      color: '#2080f0'
    },
    {
      option_value: 'stocking',
      option_label: '在库',
      sort: 2,
      remark: '货物已入库',
      type: 'success',
      color: '#18a058'
    },
    {
      option_value: 'sold',
      option_label: '已售',
      sort: 3,
      remark: '货物已售出',
      type: 'warning',
      color: '#f0a020'
    },
    {
      option_value: 'returned',
      option_label: '已退',
      sort: 4,
      remark: '货物已退回',
      type: 'error',
      color: '#d03050'
    }
  ],

  'order_status': [
    {
      option_value: 'pending',
      option_label: '待处理',
      sort: 1,
      remark: '订单待处理',
      type: 'warning',
      color: '#f0a020'
    },
    {
      option_value: 'confirmed',
      option_label: '已确认',
      sort: 2,
      remark: '订单已确认',
      type: 'info',
      color: '#2080f0'
    },
    {
      option_value: 'delivered',
      option_label: '已交付',
      sort: 3,
      remark: '订单已交付',
      type: 'success',
      color: '#18a058'
    },
    {
      option_value: 'canceled',
      option_label: '已取消',
      sort: 4,
      remark: '订单已取消',
      type: 'error',
      color: '#d03050'
    },
    {
      option_value: 'archived',
      option_label: '已归档',
      sort: 5,
      remark: '订单已归档',
      type: 'default',
      color: '#909399'
    }
  ]
}
