const fs = require('fs');
const path = require('path');
const COS = require('cos-nodejs-sdk-v5');
require('dotenv').config({ path: path.resolve(__dirname, '../.env.production') });

// 从环境变量读取配置
const config = {
    SecretId: process.env.VITE_COS_SECRET_ID,
    SecretKey: process.env.VITE_COS_SECRET_KEY,
    Bucket: process.env.VITE_COS_BUCKET,
    Region: process.env.VITE_COS_REGION
};

// 验证配置
if (!config.SecretId || !config.SecretKey || !config.Bucket || !config.Region) {
    console.error('❌ COS配置不完整，请检查.env.production文件');
    process.exit(1);
}

const cos = new COS({
    SecretId: config.SecretId,
    SecretKey: config.SecretKey,
    Promises: true
});

// 获取文件MIME类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html',
        '.css': 'text/css',
        '.js': 'application/javascript',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.woff': 'font/woff',
        '.woff2': 'font/woff2',
        '.ttf': 'font/ttf',
        '.eot': 'application/vnd.ms-fontobject'
    };
    return mimeTypes[ext] || 'application/octet-stream';
}

// 递归上传目录
async function uploadDir(dirPath, prefix = '') {
    const files = fs.readdirSync(dirPath);
    const uploadPromises = [];

    for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            // 递归上传子目录
            await uploadDir(filePath, path.posix.join(prefix, file));
        } else {
            // 上传文件
            const key = path.posix.join(prefix, file);
            const mimeType = getMimeType(filePath);

            const uploadPromise = new Promise((resolve, reject) => {
                cos.putObject({
                    Bucket: config.Bucket,
                    Region: config.Region,
                    Key: key,
                    Body: fs.createReadStream(filePath),
                    ContentType: mimeType,
                    CacheControl: key === 'index.html'
                        ? 'no-store, no-cache, must-revalidate, proxy-revalidate'
                        : mimeType.startsWith('text/') || mimeType === 'application/javascript'
                            ? 'no-cache'
                            : 'max-age=31536000', // 静态资源缓存1年
                    // 为index.html添加时间戳参数，强制刷新
                    MetadataDirective: 'REPLACE',
                    Metadata: key === 'index.html' ? {
                        'deploy-timestamp': new Date().toISOString()
                    } : undefined
                }, (err, data) => {
                    if (err) {
                        console.error(`❌ 上传失败: ${key}`, err.message);
                        reject(err);
                    } else {
                        console.log(`✅ 上传成功: ${key}`);
                        resolve(data);
                    }
                });
            });

            uploadPromises.push(uploadPromise);
        }
    }

    // 等待当前目录所有文件上传完成
    if (uploadPromises.length > 0) {
        try {
            await Promise.all(uploadPromises);
        } catch (error) {
            console.error('❌ 批量上传失败:', error.message);
            throw error;
        }
    }
}

// 创建备份目录
async function backupCurrentFiles() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPrefix = `backup/${timestamp}`;
    
    console.log('📦 开始备份当前文件...');
    
    try {
        // 列出当前根目录下的所有文件
        const listResponse = await new Promise((resolve, reject) => {
            cos.getBucket({
                Bucket: config.Bucket,
                Region: config.Region,
                Prefix: ''
            }, (err, data) => {
                if (err) reject(err);
                else resolve(data);
            });
        });

        if (listResponse.Contents.length === 0) {
            console.log('ℹ️ 当前存储桶为空，无需备份');
            return;
        }

        // 复制所有文件到备份目录
        for (const file of listResponse.Contents) {
            if (!file.Key.startsWith('backup/')) { // 跳过备份目录
                await new Promise((resolve, reject) => {
                    cos.putObjectCopy({
                        Bucket: config.Bucket,
                        Region: config.Region,
                        Key: `${backupPrefix}/${file.Key}`,
                        CopySource: `${config.Bucket}.cos.${config.Region}.myqcloud.com/${file.Key}`
                    }, (err, data) => {
                        if (err) reject(err);
                        else resolve(data);
                    });
                });
            }
        }

        // 设置备份目录的生命周期规则
        await new Promise((resolve, reject) => {
            cos.putBucketLifecycle({
                Bucket: config.Bucket,
                Region: config.Region,
                Rules: [{
                    ID: `delete-backup-${timestamp}`,
                    Status: 'Enabled',
                    Filter: {
                        Prefix: `${backupPrefix}/`
                    },
                    Expiration: {
                        Days: 7
                    }
                }]
            }, (err, data) => {
                if (err) reject(err);
                else resolve(data);
            });
        });

        console.log(`✅ 备份完成，文件已保存到 ${backupPrefix}/`);
        console.log('⏰ 备份文件将在7天后自动删除');
    } catch (error) {
        console.error('❌ 备份过程中发生错误:', error.message);
        throw error;
    }
}

// 主函数
async function main() {
    const distPath = path.resolve(__dirname, '../dist');

    if (!fs.existsSync(distPath)) {
        console.error('❌ dist目录不存在，请先执行构建命令');
        process.exit(1);
    }

    console.log('🚀 开始上传到COS...');
    console.log(`📁 源目录: ${distPath}`);
    console.log(`🪣 目标存储桶: ${config.Bucket}`);
    console.log(`🌍 区域: ${config.Region}`);
    console.log('');

    try {
        // 先执行备份
        await backupCurrentFiles();
        console.log('');
        
        // 然后上传新文件
        await uploadDir(distPath);
        console.log('');
        console.log('🎉 所有文件上传完成！');
        console.log(`🔗 访问地址: https://${config.Bucket}.cos.${config.Region}.myqcloud.com/`);

        if (process.env.VITE_CDN_BASE_URL) {
            console.log(`🚀 CDN地址: ${process.env.VITE_CDN_BASE_URL}`);
        }
    } catch (error) {
        console.error('❌ 上传过程中发生错误:', error.message);
        process.exit(1);
    }
}

// 执行主函数
main().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
});