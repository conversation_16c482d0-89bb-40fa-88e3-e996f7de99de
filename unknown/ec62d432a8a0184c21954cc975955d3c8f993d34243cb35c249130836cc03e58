<template>
  <n-modal
    :show="visible"
    @update:show="handleVisibleChange"
    preset="card"
    title="在售车型"
    :style="isMaximized ? { width: '90%', height: '90%' } : { width: '800px' }"
    :mask-closable="false"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component :is="isMaximized ? ContractOutline : ExpandOutline" />
          </n-icon>
        </template>
      </n-button>
    </template>

    <div class="selector-content">
      <!-- 搜索区域 -->
      <div class="search-area">
        <div class="search-wrapper">
          <n-input
            v-model:value="searchKeyword"
            placeholder="请输入品牌/车系/配置/SKU_ID进行搜索"
            clearable
            style="width: 300px;"
            @keydown.enter="handleSearch"
          >
            <template #suffix>
              <n-button text @click="handleSearch">
                <template #icon>
                  <n-icon>
                    <SearchOutline />
                  </n-icon>
                </template>
              </n-button>
            </template>
          </n-input>
          <n-text depth="3" class="search-tip">
            <n-icon size="14" style="margin-right: 4px;">
              <InformationCircleOutline />
            </n-icon>
            双击数据列表可以选中该数据
          </n-text>
        </div>
      </div>

      <!-- 数据列表 -->
      <div class="data-list">
        <n-spin :show="loading">
          <n-data-table
            ref="tableRef"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :row-key="row => row.id"
            :row-class-name="rowClassName"
            :row-props="rowProps"
            :checked-row-keys="selectedRowKeys"
            @update:page="handlePageChange"
            @update:page-size="handlePageSizeChange"
            @update:sorter="handleSorterChange"
            @update:checked-row-keys="handleCheckedRowKeysChange"
          />
        </n-spin>
      </div>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">取消</n-button>
        <n-button type="primary" :disabled="!selectedRow" @click="handleConfirm">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, watch, defineComponent, h } from 'vue'
import { NModal, NButton, NIcon, NSpin, NDataTable, NInput, NSpace, NTag, NText } from 'naive-ui'
import { ContractOutline, ExpandOutline, SearchOutline, InformationCircleOutline } from '@vicons/ionicons5'
import skuApi from '@/api/sku'
import messages from '@/utils/messages'

// 显式声明组件，消除IDE警告
defineComponent({
  components: {
    NModal, NButton, NIcon, NSpin, NDataTable, NInput, NSpace, NTag, NText,
    ContractOutline, ExpandOutline, SearchOutline, InformationCircleOutline
  }
})

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  // 可选参数，用于初始化筛选条件
  params: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'select'])

// 状态变量
const loading = ref(false)
const isMaximized = ref(true)
const searchKeyword = ref('')
const tableRef = ref(null) // 表格引用
const selectedRowKeys = ref([])
const selectedRow = ref(null)

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  onChange: (page) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  },
  prefix: ({ itemCount }) => `共 ${itemCount} 条`
})

// 表格数据
const tableData = ref([])

// 表格列配置
const createColumns = () => {
  return [
    {
      type: 'selection',
      width: 50,
      multiple: false
    },
    {
      title: '品牌',
      key: 'brand',
      width: 120
    },
    {
      title: '车系',
      key: 'series',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '配置',
      key: 'configName',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '颜色代码',
      key: 'colorCode',
      width: 80
    },
    {
      title: '启票价(元)',
      key: 'sbPrice',
      width: 100,
      render: (row) => {
        // 添加￥前缀并使用千分位展示
        const formattedPrice = row.sbPrice ? `￥${Number(row.sbPrice).toLocaleString('zh-CN')}` : '0';
        return h(
          'span',
          {
            style: {
              color: '#f0a020',
              fontWeight: 'bold'
            }
          },
          formattedPrice
        )
      }
    }
  ]
}

const columns = createColumns()

// 切换弹窗大小
const toggleSize = () => {
  isMaximized.value = !isMaximized.value
}

// 处理弹窗显示状态变化
const handleVisibleChange = (value) => {
  emit('update:visible', value)
  if (!value) {
    // 关闭弹窗时重置状态
    resetState()
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 确认选择
const handleConfirm = () => {
  if (selectedRow.value) {
    // 获取详细信息后返回
    fetchDetailAndReturn(selectedRow.value.id)
  } else {
    messages.warning('请选择一条记录')
  }
}

// 获取详细信息并返回
const fetchDetailAndReturn = async (id) => {
  loading.value = true
  try {
    const response = await skuApi.getSkuDetail(id)
    if (response.code === 200) {
      // 返回详细信息
      emit('select', response.data)
      handleClose()
    } else {
      messages.error(response.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    messages.error('获取详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page
  fetchData()
}

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchData()
}

// 处理排序变化
const handleSorterChange = () => {
  fetchData()
}

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  // 直接更新选中状态
  selectedRowKeys.value = keys

  // 更新选中行数据
  if (keys.length > 0) {
    selectedRow.value = tableData.value.find(item => item.id === keys[0])
  } else {
    selectedRow.value = null
  }

  // 强制更新视图
  tableData.value = [...tableData.value]
}

// 行样式
const rowClassName = (row) => {
  // 检查行是否被选中
  if (selectedRowKeys.value.includes(row.id)) {
    return 'selected-row'
  }
  return ''
}

// 行属性函数 - 添加双击事件和选中样式
const rowProps = (row) => {
  return {
    onClick: () => {
      // 单击选中行
      selectedRowKeys.value = [row.id]
      selectedRow.value = row
    },
    onDblclick: () => {
      // 双击选中并确认
      selectedRowKeys.value = [row.id]
      selectedRow.value = row
      handleConfirm()
    }
  }
}


// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
      keywords: searchKeyword.value || undefined,
      ...props.params
    }

    const response = await skuApi.getSkuList(params)

    if (response.code === 200) {
      tableData.value = response.data.list
      pagination.itemCount = response.data.total
      pagination.pageCount = response.data.pages
    } else {
      messages.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    messages.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置状态
const resetState = () => {
  searchKeyword.value = ''
  selectedRowKeys.value = []
  selectedRow.value = null
  pagination.page = 1
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 打开弹窗时获取数据
      fetchData()
    }
  }
)


</script>

<style scoped>
.selector-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
}

.search-area {
  margin-bottom: 16px;
  width: 100%;
}

.search-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.search-tip {
  margin-left: 12px;
  display: flex;
  align-items: center;
  font-size: 13px;
}

.data-list {
  flex: 1;
  overflow: auto;
}

:deep(.selected-row) {
  background-color: rgba(24, 160, 88, 0.1) !important;
  border-left: 3px solid #18a058;
}

:deep(.n-data-table-table) {
  cursor: pointer;
}

:deep(.n-data-table-td) {
  padding: 8px 12px;
}

:deep(.n-data-table-th) {
  padding: 10px 12px;
  background-color: #f5f7fa;
}

/* 鼠标悬停样式 */
:deep(.n-data-table-tr:hover) {
  background-color: rgba(24, 160, 88, 0.05);
}

/* 选中行的悬停样式 */
:deep(.n-data-table-tr.selected-row:hover) {
  background-color: rgba(24, 160, 88, 0.15) !important;
}

/* 确保选中行在条纹表格中也能正确显示 */
:deep(.n-data-table-tr.n-data-table-tr--striped.selected-row) {
  background-color: rgba(24, 160, 88, 0.1) !important;
}
</style>
