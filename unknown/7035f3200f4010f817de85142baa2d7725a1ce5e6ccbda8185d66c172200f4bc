<template>
  <div class="accounts-page">
    <n-space vertical :size="24">
      <n-card title="">
        <n-grid :cols="12" :x-gap="24">
          <!-- 左侧组织机构树 -->
          <n-grid-item span="4">
            <n-card class="org-tree-card">
              <template #header>
                <div class="org-tree-header">
                  <span>组织机构</span>
                  <div class="search-container">
                    <n-input
                      v-model:value="deptSearchKeyword"
                      placeholder="快速搜索"
                      clearable
                      size="small"
                      @keydown.enter="searchDepartment"
                      @clear="clearDeptSearch"
                      style="width: 220px"
                    >
                      <template #suffix>
                        <n-icon style="cursor: pointer" @click="searchDepartment"><SearchOutline /></n-icon>
                      </template>
                    </n-input>
                  </div>
                </div>
              </template>
              <div class="org-tree-container">
                <n-tree
                  block-line
                  :data="filteredDepartmentTree"
                  :expanded-keys="expandedKeys"
                  :selected-keys="selectedDepartmentKeys"
                  :render-suffix="renderSuffix"
                  :show-switcher="false"
                  :override-default-node-click-behavior="overrideNodeClickBehavior"
                  selectable
                  @update:expanded-keys="handleTreeExpand"
                />
              </div>
            </n-card>
          </n-grid-item>

          <!-- 右侧账户列表 -->
          <n-grid-item span="8">
            <n-card :title="accountListTitle" class="account-list-card">
              <template #header-extra>
                <n-space>
                  <n-input
                    v-model:value="searchParams.keywords"
                    placeholder="请输入账户名称或账号"
                    clearable
                    style="width: 300px;"
                    @keydown.enter="searchData"
                  >
                    <template #prefix>
                      <n-icon><SearchOutline /></n-icon>
                    </template>
                  </n-input>
                  <n-button @click="searchData" type="primary">
                    <template #icon>
                      <n-icon><SearchOutline /></n-icon>
                    </template>
                    搜索
                  </n-button>
                  <n-button @click="refreshData" secondary>
                    <template #icon>
                      <n-icon><RefreshOutline /></n-icon>
                    </template>
                    刷新
                  </n-button>
                  <n-button @click="handleAddAccount" type="info">
                    <template #icon>
                      <n-icon><AddCircleOutline /></n-icon>
                    </template>
                    新增
                  </n-button>

                </n-space>
              </template>
              <div class="account-table-container">
                <n-data-table
                  ref="tableRef"
                  :columns="columns"
                  :data="accountsData"
                  :pagination="pagination"
                  :loading="loading"
                  :row-key="row => row.id"
                  @update:page="handlePageChange"
                  @update:page-size="handlePageSizeChange"
                  :scroll-x="1200"
                />
              </div>
            </n-card>
          </n-grid-item>
        </n-grid>
      </n-card>

    </n-space>
  </div>
</template>

<script setup>
import { ref, onMounted, h, reactive, computed } from 'vue'
import {
  NSpace, NCard, NGrid, NGridItem, NDataTable, NButton,
  NInput, NSwitch, NIcon, NTree, NDropdown,
  useDialog, NTag, NInputNumber
} from 'naive-ui'
import { getDepartments } from '@/api/users'
import { accountsApi } from '@/api/accounts'
import {
  AddCircleOutline, SearchOutline, RefreshOutline
} from '@vicons/ionicons5'
import { Edit, TrashCan } from '@vicons/carbon'
import { MoreVertical16Filled } from '@vicons/fluent'
import messages from '@/utils/messages'
import { formatMoney } from '@/utils/money'

// 状态变量
const departmentTree = ref([])
const selectedDepartmentKeys = ref([])
const accountsData = ref([])
const pagination = ref({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})


const expandedKeys = ref([])
const loading = ref(false)
const tableRef = ref(null)
const dialog = useDialog()
const currentOrgName = ref('全部')
const editableRowKeys = ref([]) // 当前可编辑的行的key集合

// 部门搜索相关
const deptSearchKeyword = ref('') // 搜索关键字
const originalExpandedKeys = ref([]) // 保存原始展开状态

// 计算属性
const accountListTitle = computed(() => {
  const selectedDept = findNodeByKey(departmentTree.value, selectedDepartmentKeys.value[0])
  const deptAbbr = selectedDept?.rawData?.deptAbbr || currentOrgName.value
  return `${deptAbbr}的账户列表`
})

// 过滤后的部门树
const filteredDepartmentTree = computed(() => {
  if (!deptSearchKeyword.value.trim()) {
    return departmentTree.value
  }

  // 搜索关键字
  const keyword = deptSearchKeyword.value.toLowerCase().trim()

  // 递归搜索函数
  const searchNodes = (nodes) => {
    if (!nodes || nodes.length === 0) return []

    return nodes.reduce((result, node) => {
      // 检查当前节点是否匹配
      const isMatch = node.label.toLowerCase().includes(keyword)

      // 递归搜索子节点
      const matchedChildren = searchNodes(node.children)

      // 如果当前节点匹配或者有匹配的子节点，则保留该节点
      if (isMatch || matchedChildren.length > 0) {
        // 创建节点的副本，避免修改原始数据
        const newNode = { ...node }

        // 如果有匹配的子节点，则替换子节点
        if (matchedChildren.length > 0) {
          newNode.children = matchedChildren
        }

        result.push(newNode)
      }

      return result
    }, [])
  }

  return searchNodes(departmentTree.value)
})

// 格式化金额显示（分转元，添加千分位分隔符）
const formatAmount = (amount) => {
  // 将分转换为元并格式化
  return formatMoney(amount ? amount / 100 : 0)
}

// 搜索参数
const searchParams = reactive({
  ownerOrgId: null,
  keywords: '',
  page: 1,
  size: 20
})

// 表格列定义
const columns = [
  {
    title: '账户名称',
    key: 'abbr',
    width: 200,
    fixed: 'left',
    ellipsis: {
      tooltip: true
    },
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NInput, {
          value: row.abbr,
          maxlength: 20,
          showCount: true,
          onUpdateValue(v) {
            accountsData.value[index].abbr = v
          }
        })
      }

      return h('span', row.abbr)
    }
  },
  {
    title: '可收款',
    key: 'receivable',
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSwitch, {
          value: row.receivable,
          onUpdateValue(v) {
            accountsData.value[index].receivable = v
          }
        })
      }

      return h(NTag, { type: row.receivable ? 'success' : 'default' }, {
        default: () => row.receivable ? '是' : '否'
      })
    }
  },
  {
    title: '可付款',
    key: 'payable',
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSwitch, {
          value: row.payable,
          onUpdateValue(v) {
            accountsData.value[index].payable = v
          }
        })
      }

      return h(NTag, { type: row.payable ? 'success' : 'default' }, {
        default: () => row.payable ? '是' : '否'
      })
    }
  },
  {
    title: '账户状态',
    key: 'usable',
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSwitch, {
          value: row.usable,
          onUpdateValue(v) {
            accountsData.value[index].usable = v
          }
        })
      }

      return h(NTag, { type: row.usable ? 'success' : 'error' }, {
        default: () => row.usable ? '启用' : '停用'
      })
    }
  },
  {
    title: '期初金额',
    key: 'initAmount',
    width: 150,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NInputNumber, {
          value: (row.initialAmount || row.initAmount || 0) / 100, // 分转元
          min: -*********, // 允许负数
          precision: 2, // 保留2位小数
          step: 1,
          buttonPlacement: 'both',
          onUpdateValue(v) {
            // 元转分，并保存
            accountsData.value[index].initialAmount = Math.round((v || 0) * 100)
            // 同时更新initAmount字段，用于API提交
            accountsData.value[index].initAmount = Math.round((v || 0) * 100)
          }
        })
      }

      // 显示为千分位格式
      return h('span', formatAmount(row.initAmount || 0))
    }
  },
  {
    title: '备注',
    key: 'remark',
    width: 250,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NInput, {
          value: row.remark,
          maxlength: 100,
          showCount: true,
          onUpdateValue(v) {
            accountsData.value[index].remark = v
          }
        })
      }

      return h('span', row.remark || '-')
    }
  },
  {
    title: '经办人',
    key: 'editorName',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right',
    render: (row, index) => {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id)

      if (isEditing) {
        return h(NSpace, { align: 'center' }, {
          default: () => [
            h(NButton, {
              quaternary: true,
              circle: true,
              size: 'small',
              type: 'success',
              onClick: () => saveRow(row),
              style: 'color: #18a058; font-size: 18px;'
            }, { default: () => h(NIcon, { component: CheckmarkCircleOutline }) }),
            h(NButton, {
              quaternary: true,
              circle: true,
              size: 'small',
              type: 'error',
              onClick: () => cancelEdit(row),
              style: 'color: #d03050; font-size: 18px;'
            }, { default: () => h(NIcon, { component: CloseCircleOutline }) })
          ]
        })
      }

      return h(NSpace, { align: 'center' }, {
        default: () => [
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => editRow(row)
          }, { default: () => h(NIcon, { component: Edit }) }),
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => deleteRow(row, index),
            disabled: row.usable === true,
            title: row.usable ? '启用状态的账户不能删除' : '删除账户',
            style: row.usable ? 'cursor: not-allowed; opacity: 0.5;' : ''
          }, { default: () => h(NIcon, { component: TrashCan }) })
        ]
      })
    }
  }
]

onMounted(async () => {
  await fetchDepartments()
  if (departmentTree.value.length > 0) {
    selectedDepartmentKeys.value = [departmentTree.value[0].key]
    // 获取所有节点的key，使树默认全部展开
    expandedKeys.value = getAllKeys(departmentTree.value)
    await fetchAccounts(selectedDepartmentKeys.value[0])
  }
})

// 构建树形数据，显示所有层级
function buildTreeData(departments) {
  const options = []
  const map = {}

  // 第一步：创建所有节点的映射
  departments.forEach(dept => {
    map[dept.id] = {
      key: dept.id.toString(),
      label: dept.name,
      children: [],
      isLeaf: true, // 初始假设所有节点都是叶子节点
      rawData: dept // 保存原始数据，方便后续使用
    }
  })

  // 第二步：构建树形结构
  departments.forEach(dept => {
    if (dept.parentId === 0 || !dept.parentId) {
      // 根节点直接添加到结果中
      options.push(map[dept.id])
    } else {
      // 子节点添加到其父节点下
      const parent = map[dept.parentId]
      if (parent) {
        parent.children.push(map[dept.id])
        parent.isLeaf = false // 如果有子节点，则不是叶子节点
      } else {
        // 如果找不到父节点，将其作为根节点处理
        console.warn(`Parent node with ID ${dept.parentId} not found for department ${dept.name} (ID: ${dept.id}). Treating as root node.`)
        options.push(map[dept.id])
      }
    }
  })

  return options
}

// 获取部门数据
async function fetchDepartments() {
  try {
    const response = await getDepartments()
    departmentTree.value = buildTreeData(response.data)
    if (departmentTree.value.length > 0) {
      selectedDepartmentKeys.value = [departmentTree.value[0].key]
      // 初始化 expandedKeys 为所有节点的 key，使树默认全部展开
      expandedKeys.value = getAllKeys(departmentTree.value)
      await fetchAccounts(selectedDepartmentKeys.value[0])
    }
  } catch (error) {
    console.error('Failed to fetch departments:', error)
    messages.error('获取组织机构数据失败')
  }
}

// 获取账户数据
async function fetchAccounts(departmentId) {
  loading.value = true
  try {
    // 更新搜索参数
    searchParams.ownerOrgId = departmentId // 使用ownerOrgId作为参数名
    searchParams.page = pagination.value.page
    searchParams.size = pagination.value.pageSize

    // 更新表格标题，显示当前选中的机构名称
    const selectedDept = findNodeByKey(departmentTree.value, departmentId)
    currentOrgName.value = selectedDept ? selectedDept.label : '全部'

    // 调用真实API
    const response = await accountsApi.getList(searchParams)

    // 处理返回的数据结构
    if (response && response.code === 200 && response.data) {
      accountsData.value = response.data.list || []
      pagination.value.itemCount = response.data.total || 0
      pagination.value.page = response.data.pageNum || 1
      pagination.value.pageCount = response.data.pages || 1
    } else {
      accountsData.value = []
      pagination.value.itemCount = 0
    }
  } catch (error) {
    console.error('Failed to fetch accounts:', error)
    messages.error('获取账户数据失败')
    accountsData.value = []
  } finally {
    loading.value = false
  }
}

// 添加一个辅助函数来查找节点
function findNodeByKey(tree, key) {
  for (const node of tree) {
    if (node.key === key) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeByKey(node.children, key)
      if (found) return found
    }
  }
  return null
}

// 分页处理
function handlePageChange(page) {
  pagination.value.page = page
  searchParams.page = page
  fetchAccounts(selectedDepartmentKeys.value[0])
}

function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  searchParams.page = 1
  searchParams.size = pageSize
  fetchAccounts(selectedDepartmentKeys.value[0])
}



// 添加新行
function handleAddAccount() {
  // 创建一个新的账户行
  const newAccount = {
    id: `temp_${Date.now()}`, // 临时ID，保存时会被替换
    abbr: '',
    receivable: true,
    payable: true,
    usable: true, // 账户状态，默认为启用
    initialAmount: 0, // 期初金额，默认0（用于编辑）
    initAmount: 0, // 期初金额，默认0（用于API）
    remark: '',
    creatorName: JSON.parse(localStorage.getItem('user') || '{}').nickname || '当前用户', // 从localStorage获取当前登录用户名
    ownerOrgId: selectedDepartmentKeys.value[0],
    isNew: true // 标记为新行
  }

  // 添加到数据列表
  accountsData.value.push(newAccount)

  // 设置为编辑状态
  editableRowKeys.value.push(newAccount.id)

  // 滚动到底部
  setTimeout(() => {
    if (tableRef.value) {
      const tableEl = tableRef.value.$el.querySelector('.n-data-table-body')
      if (tableEl) {
        tableEl.scrollTop = tableEl.scrollHeight
      }
    }
  }, 100)
}

// 编辑行
function editRow(row) {
  editableRowKeys.value.push(row.id)
}

// 取消编辑
function cancelEdit(row) {
  // 从可编辑行集合中移除
  editableRowKeys.value = editableRowKeys.value.filter(id => id !== row.id)

  // 如果是新添加的行，则从数据中移除
  if (row.isNew) {
    accountsData.value = accountsData.value.filter(item => item.id !== row.id)
  } else {
    // 如果是编辑现有行，则重新获取数据
    fetchAccounts(selectedDepartmentKeys.value[0])
  }
}

// 保存行
async function saveRow(row) {
  // 验证必填字段
  if (!row.abbr) {
    messages.error('账户名称不能为空')
    return
  }

  // 验证字段长度
  if (row.abbr.length > 20) {
    messages.error('账户名称不能超过20个字符')
    return
  }

  if (row.remark && row.remark.length > 100) {
    messages.error('备注不能超过100个字符')
    return
  }

  // 验证期初金额
  if ((row.initialAmount === undefined || row.initialAmount === null) &&
      (row.initAmount === undefined || row.initAmount === null)) {
    messages.error('期初金额不能为空')
    return
  }

  try {
    if (row.isNew) {
      // 创建新账户
      const newAccount = {
        abbr: row.abbr,
        receivable: row.receivable,
        payable: row.payable,
        usable: row.usable, // 账户状态
        initAmount: row.initialAmount, // 期初金额（分）
        remark: row.remark,
        ownerOrgId: row.ownerOrgId
      }

      // 调用API创建账户
      await accountsApi.create(newAccount)
      messages.success('账户创建成功')
    } else {
      // 更新现有账户
      const updatedAccount = {
        id: row.id,
        abbr: row.abbr,
        receivable: row.receivable,
        payable: row.payable,
        usable: row.usable, // 账户状态
        initAmount: row.initialAmount, // 期初金额（分）
        remark: row.remark
      }

      // 调用API更新账户
      await accountsApi.update(updatedAccount)
      messages.success('账户更新成功')
    }

    // 从可编辑行集合中移除
    editableRowKeys.value = editableRowKeys.value.filter(id => id !== row.id)

    // 重新获取数据
    await fetchAccounts(selectedDepartmentKeys.value[0])
  } catch (error) {
    console.error('Failed to save account:', error)
    messages.error('保存账户失败')
  }
}

// 删除行
function deleteRow(row, index) {
  // 检查账户是否处于启用状态
  if (row.usable === true) {
    messages.warning('启用状态的账户不能删除，请先停用该账户')
    return
  }

  dialog.warning({
    title: '删除确认',
    content: `确定要删除账户"${row.abbr}"吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        if (!row.isNew) {
          // 如果不是新行，则调用API删除
          await accountsApi.delete(row.id)
        }

        // 从数据中移除
        accountsData.value.splice(index, 1)
        messages.success('账户删除成功')
      } catch (error) {
        console.error('Failed to delete account:', error)
        messages.error('删除账户失败')
      }
    }
  })
}





// 刷新数据
function refreshData() {
  pagination.value.page = 1
  searchParams.page = 1
  fetchAccounts(selectedDepartmentKeys.value[0])
}

// 搜索数据
function searchData() {
  pagination.value.page = 1
  searchParams.page = 1
  fetchAccounts(selectedDepartmentKeys.value[0])
}



// 树节点点击行为
const overrideNodeClickBehavior = ({ option }) => {
  // 更新选中状态
  selectedDepartmentKeys.value = [option.key]

  // 加载账户列表
  fetchAccounts(option.key)

  // 返回 'prevent-default' 来阻止默认行为
  return 'prevent-default'
}

// 树节点展开处理
function handleTreeExpand(keys) {
  expandedKeys.value = keys
}

// 搜索部门
function searchDepartment() {
  if (!deptSearchKeyword.value.trim()) {
    clearDeptSearch()
    return
  }

  // 保存当前展开状态
  if (originalExpandedKeys.value.length === 0) {
    originalExpandedKeys.value = [...expandedKeys.value]
  }

  // 展开所有节点以显示搜索结果
  const allKeys = getAllKeys(departmentTree.value)
  expandedKeys.value = allKeys
}

// 清除搜索
function clearDeptSearch() {
  deptSearchKeyword.value = ''

  // 恢复原始展开状态
  if (originalExpandedKeys.value.length > 0) {
    expandedKeys.value = [...originalExpandedKeys.value]
    originalExpandedKeys.value = []
  }
}

// 获取所有节点的key
function getAllKeys(nodes) {
  if (!nodes || nodes.length === 0) return []

  return nodes.reduce((keys, node) => {
    keys.push(node.key)
    if (node.children && node.children.length > 0) {
      keys.push(...getAllKeys(node.children))
    }
    return keys
  }, [])
}


// 渲染树节点后缀
function renderSuffix({ option }) {
  return () => h(
    'div',
    { class: 'tree-node-action' },
    h(
      NDropdown,
      {
        trigger: 'click',
        options: [
          {
            label: '新增',
            key: 'add',
            icon: renderIcon(AddCircleOutline)
          }
        ],
        onSelect: (key) => {
          if (key === 'add') {
            // 创建一个新的账户行
            const newAccount = {
              id: `temp_${Date.now()}`, // 临时ID，保存时会被替换
              abbr: '',
              receivable: true,
              payable: true,
              usable: true, // 账户状态，默认为启用
              initialAmount: 0, // 期初金额，默认0（用于编辑）
              initAmount: 0, // 期初金额，默认0（用于API）
              remark: '',
              creatorName: JSON.parse(localStorage.getItem('user') || '{}').nickname || '当前用户', // 从localStorage获取当前登录用户名
              ownerOrgId: option.key,
              isNew: true // 标记为新行
            }

            // 添加到数据列表
            accountsData.value.push(newAccount)

            // 设置为编辑状态
            editableRowKeys.value.push(newAccount.id)

            // 滚动到底部
            setTimeout(() => {
              if (tableRef.value) {
                const tableEl = tableRef.value.$el.querySelector('.n-data-table-body')
                if (tableEl) {
                  tableEl.scrollTop = tableEl.scrollHeight
                }
              }
            }, 100)
          }
        }
      },
      {
        default: () => h(NIcon, { component: MoreVertical16Filled })
      }
    )
  )
}

// 渲染图标
function renderIcon(icon) {
  return () => h(NIcon, null, { default: () => h(icon) })
}
</script>

<style scoped>
.accounts-page {
  padding: 16px;
}

.org-tree-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.org-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.org-tree-header span {
  font-size: 16px;
  font-weight: 500;
}

.search-container {
  display: flex;
  align-items: center;
}

.org-tree-container {
  height: 80vh; /* 设置为视口高度的90% */
  overflow-y: auto; /* 垂直方向滚动 */
  overflow-x: hidden; /* 水平方向隐藏 */
  padding-right: 8px; /* 为滚动条预留空间 */
}

/* 自定义滚动条样式 */
.org-tree-container::-webkit-scrollbar {
  width: 6px;
}

.org-tree-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.org-tree-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.org-tree-container::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

.account-list-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.account-table-container {
  height: 550px; /* 设置固定高度，略小于树容器，为分页留出空间 */
  overflow: auto; /* 允许在两个方向上滚动 */
}

/* 自定义滚动条样式 */
.account-table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.account-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.account-table-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.account-table-container::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

.n-tree {
  --n-item-height: 40px;
}

/* 添加以下样式以调整操作列的布局 */
.n-data-table .n-button.n-button--quaternary {
  padding: 0;
  margin: 0 4px;
}

/* 可以添加以下样式来调整展开/收起图标的大小 */
.n-tree .n-tree-node-switcher {
  transform: none !important;
}

/* 调整图标大小和对齐方式 */
.n-tree .n-tree-node-switcher svg {
  font-size: 18px;
  width: 1em;
  height: 1em;
}

/* 确保叶子节点没有左边 */
.n-tree .n-tree-node-content__prefix {
  width: auto;
}

/* 隐藏叶子节点的切换器 */
.n-tree .n-tree-node--leaf .n-tree-node-switcher {
  visibility: hidden;
  width: 0;
}

/* 修改节点操作按钮的样式 */
.n-tree .n-tree-node-content {
  position: relative;
}

.tree-node-action {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.n-tree-node:hover .tree-node-action,
.n-tree-node--selected .tree-node-action {
  opacity: 1;
}

.tree-node-action .n-icon {
  font-size: 16px;
}

/* 确保下拉菜单在树节点之上 */
.n-dropdown-menu {
  z-index: 1000;
}
</style>