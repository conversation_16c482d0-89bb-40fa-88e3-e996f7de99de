<template>
  <div class="start-bill-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">启票日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">车辆类别</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.vehicleCategory"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in vehicleCategoryOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">启票单位</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><Building /></n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <!-- 显示选中的机构标签 - 在清空按钮右侧 -->
            <div
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in filterForm.invoiceOrgs"
                :key="org.id"
                type="success"
                size="small"
                closable
                @close="removeOrg(org)"
                style="margin: 2px 4px 2px 0"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增启票
        </n-button>

        <n-button
          type="success"
          @click="handleComplete"
          round
          :disabled="selectedRows.length === 0"
        >
          <template #icon>
            <n-icon><CheckmarkDoneOutline /></n-icon>
          </template>
          启票完成
        </n-button>

        <file-upload-button
          button-type="info"
          button-text="Excel导入"
          button-mode="standard"
          accept-formats=".xlsx,.xls"
          :max-size="10"
          template-url="/templates/start-bill-template.xlsx"
          :button-style="{ fontWeight: 'bold' }"
          @success="handleImportSuccess"
          @error="handleImportError"
        />

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入VIN"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
          @clear="handleClear"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :pagination="false"
          :scroll-x="1200"
          :max-height="tableMaxHeight"
          :sticky="true"
          @update:checked-row-keys="handleSelectionChange"
        />
      </div>

      <!-- 分页组件 - 固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size="medium"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <start-bill-form-modal
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :edit-data="currentEditData"
      @success="refreshData"
    />

    <!-- 详情弹窗 -->
    <start-bill-detail-modal
      v-model:visible="detailDialogVisible"
      :id="currentDetailId"
    />

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择启票单位"
      business-permission="can_stock_in"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import useStartBillPage from "./StartBillPage.js";

// 使用组合式函数获取所有页面逻辑
const {
  // 组件引用
  FileUploadButton,
  StartBillDetailModal,
  StartBillFormModal,
  BizOrgSelector,

  // 图标
  SearchOutline,
  RefreshOutline,
  AddOutline,
  EyeOutline,
  CreateOutline,
  CheckmarkDoneOutline,
  CopyOutline,
  Building,

  // 响应式数据
  tableRef,
  loading,
  dialogVisible,
  isEdit,
  currentEditData,
  selectedRows,
  showOrgSelector,
  vehicleCategoryOptions,
  filterForm,
  startBillData,
  pagination,
  windowHeight,
  detailDialogVisible,
  currentDetailId,

  // 计算属性
  tableMaxHeight,
  selectedOrgText,
  filteredData,

  // 表格配置
  columns,

  // 日期相关
  dateRangeOptions,

  // 业务方法
  handleDateRangeChange,
  handleCustomDateChange,
  handleSearch,
  handleClear,
  showAddDialog,
  handleEdit,
  handleView,
  handleSelectionChange,
  handlePageChange,
  handlePageSizeChange,
  handleImportSuccess,
  handleImportError,
  handleComplete,
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,
  refreshData,

  // 生命周期方法
  initialize,
  cleanup,
} = useStartBillPage();

// 生命周期钩子
onMounted(() => {
  initialize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss">
@use "./StartBillPage.scss";
</style>
