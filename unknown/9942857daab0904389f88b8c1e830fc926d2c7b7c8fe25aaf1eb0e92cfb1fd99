<template>
  <div>
    <div class="section-title">
      <span class="title-text">付款方式</span>
    </div>
    <n-divider title-placement="left"></n-divider>

    <div class="option-row">
      <span class="option-label">请选择客户的付款方式：</span>
      <n-radio-group v-model:value="form.paymentMethod">
        <n-radio-button value="FULL">全款</n-radio-button>
        <n-radio-button value="LOAN">分期</n-radio-button>
      </n-radio-group>
    </div>

    <!-- 贷款信息，仅在选择贷款时显示 -->
    <n-grid v-if="form.paymentMethod === 'LOAN'" :cols="5" :x-gap="16" :y-gap="1">
      <n-grid-item>
        <n-form-item label="分期金融机构" path="loanChannel" required>
          <n-select v-model:value="form.loanChannel" :options="loanChannelOptions" placeholder="请选择分期金融机构" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="分期金额(元)" path="loanAmount" required>
          <n-input-number v-model:value="form.loanAmount" placeholder="请输入贷款金额" style="width: 100%" :precision="2"
            :min="0" button-placement="both" @update:value="$emit('loan-amount-change')" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="首付金额(元)" path="loanInitialAmount" required>
          <n-input-number v-model:value="form.loanInitialAmount" placeholder="请输入首付金额" style="width: 100%"
            :precision="2" :min="0" button-placement="both" @update:value="$emit('loan-initial-amount-change')" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="首付比例(%)" path="loanInitialRatio">
          <n-input-number v-model:value="form.loanInitialRatio" disabled style="width: 100%" :precision="2"
            :show-button="false" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="分期时长" path="loanMonths" required>
          <n-select v-model:value="form.loanMonths" :options="loanMonthsOptions" placeholder="请选择分期期数"
            @update:value="val => {
              form.loanMonths = val || 12;
            }" />
        </n-form-item>
      </n-grid-item>

      <!-- 新增分期返利（客户）字段 -->
      <n-grid-item>
        <n-form-item label="分期返利金额(元)" path="loanRebateAmount">
          <div style="display: flex; align-items: center; width: 100%;">
            <n-input-number v-model:value="form.loanRebateAmount" placeholder="请输入分期返利" style="flex: 1;"
              :precision="2" :min="0" button-placement="both" />
            <n-checkbox v-model:checked="form.loanRebateDeductible" style="margin-left: 10px; white-space: nowrap; width: 80px;">转车款</n-checkbox>
          </div>
        </n-form-item>
      </n-grid-item>

      <!-- 新增分期服务费字段 -->
      <n-grid-item>
        <n-form-item label="分期服务费(元)" path="loanFee">
          <n-input-number v-model:value="form.loanFee" placeholder="请输入分期服务费" style="width: 100%" :precision="2"
            :min="0" button-placement="both" @update:value="$emit('loan-fee-change')" />
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  },
  loanChannelOptions: {
    type: Array,
    required: true
  },
  loanMonthsOptions: {
    type: Array,
    required: true
  }
})

// 定义组件事件
defineEmits(['loan-amount-change', 'loan-initial-amount-change', 'loan-fee-change'])
</script>
