<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">财务结算</span>
    </div>
    <n-divider class="section-divider" style="height: 2px; background-image: linear-gradient(to right, var(--primary-color, #18a058) 0%, rgba(24, 160, 88, 0.1) 100%); border: none;"></n-divider>

    <n-grid :cols="4" :x-gap="16" :y-gap="1">
      <n-grid-item>
        <n-form-item label="车辆售价(元)" path="vehicleSalePrice">
          <n-input-number v-model:value="form.vehicleSalePrice" placeholder="车辆售价" style="width: 100%" :precision="2"
            :min="0" button-placement="both" @update:value="handleSalePriceChange" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="折现总金额(元)" path="discountAmount">
          <n-input-number v-model:value="form.discountAmount" placeholder="请输入折现总金额" style="width: 100%" :precision="2"
            :min="0" button-placement="both" @update:value="handleDiscountChange" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="专享优惠类型" path="exclusiveDiscountType">
          <n-select v-model:value="form.exclusiveDiscountType" :options="exclusiveDiscountTypeOptions"
            placeholder="请选择专享优惠类型" clearable @update:value="handleExclusiveDiscountTypeChange" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="showExclusiveDiscountAmount">
        <n-form-item label="专享优惠金额(元)" path="exclusiveDiscountAmount">
          <n-input-number v-model:value="form.exclusiveDiscountAmount" placeholder="请输入专享优惠金额"
            :precision="2" :min="0" button-placement="both" @update:value="handleExclusiveDiscountAmountChange" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="衍生收入总金额(元)" path="derivativeIncomeTotal">
          <n-input-number v-model:value="derivativeIncomeTotal" disabled style="width: 100%" :precision="2"
            :show-button="false" />
        </n-form-item>
      </n-grid-item>
    </n-grid>

    <div class="customer-payable-amount">
      <span class="payable-label">应付总额(元)：</span>
      <span class="payable-value">{{ formatCurrency(finalPrice) }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { NGrid, NGridItem, NFormItem, NInputNumber, NDivider, NSelect } from 'naive-ui'
import { getDictOptions } from '@/api/dict'

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})

// 定义组件事件
const emit = defineEmits([
  'handle-sale-price-change',
  'handle-discount-change',
  'handle-exclusive-discount-change'
])

// 专享优惠类型选项
const exclusiveDiscountTypeOptions = ref([])

// 获取专享优惠类型选项
const fetchExclusiveDiscountTypeOptions = async () => {
  try {
    const res = await getDictOptions('exclusive_discount_type')
    if (res && res.code === 200 && res.data) {
      exclusiveDiscountTypeOptions.value = res.data.map(item => ({
        label: item.option_label,
        value: item.option_value
      }))
    }
  } catch (error) {
    console.error('获取专享优惠类型选项失败:', error)
  }
}

// 组件挂载时获取字典数据
onMounted(() => {
  fetchExclusiveDiscountTypeOptions()
})

// 是否显示专享优惠金额字段
const showExclusiveDiscountAmount = computed(() => {
  return props.form.exclusiveDiscountType && props.form.exclusiveDiscountType !== 'NONE'
})

// 计算衍生收入总金额
const derivativeIncomeTotal = computed(() => {
  if (props.form.hasDerivativeIncome !== 'YES') {
    return 0
  }
  
  // 计算所有衍生收入字段的总和
  return (
    (props.form.notaryFee || 0) +
    (props.form.carefreeIncome || 0) +
    (props.form.extendedWarrantyIncome || 0) +
    (props.form.vpsIncome || 0) +
    (props.form.preInterest || 0) +
    (props.form.licensePlateFee || 0) +
    (props.form.tempPlateFee || 0) +
    (props.form.deliveryEquipment || 0) +
    (props.form.otherIncome || 0)
  )
})

// 计算最终价格
const finalPrice = computed(() => {
  // 基础价格 = 车辆售价 - 折现总金额
  let price = (props.form.vehicleSalePrice || 0) - (props.form.discountAmount || 0)
  
  // 如果有专享优惠且类型不是"无"，减去专享优惠金额
  if (props.form.exclusiveDiscountType && props.form.exclusiveDiscountType !== 'NONE') {
    price -= (props.form.exclusiveDiscountAmount || 0)
  }
  
  // 加上衍生收入总金额
  price += derivativeIncomeTotal.value
  
  // 如果是分期付款，加上分期服务费
  if (props.form.paymentMethod === 'LOAN') {
    price += (props.form.loanFee || 0)
  }
  
  return price
})

// 监听衍生收入变化，更新最终价格
watch(derivativeIncomeTotal, () => {
  props.form.finalPrice = finalPrice.value
  // 更新大写金额 (在父组件中处理)
})

// 处理车辆售价变化
const handleSalePriceChange = () => {
  emit('handle-sale-price-change')
}

// 处理折现总金额变化
const handleDiscountChange = () => {
  emit('handle-discount-change')
}

// 处理专享优惠类型变化
const handleExclusiveDiscountTypeChange = () => {
  if (!props.form.exclusiveDiscountType || props.form.exclusiveDiscountType === 'NONE') {
    props.form.exclusiveDiscountAmount = 0
  }
  emit('handle-exclusive-discount-change')
}

// 处理专享优惠金额变化
const handleExclusiveDiscountAmountChange = () => {
  emit('handle-exclusive-discount-change')
}

// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null) return '¥0.00'
  return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}
</script>

<style lang="scss" scoped>
.customer-payable-amount {
  margin-top: 16px;
  padding: 8px 0;
  text-align: right;
  
  .payable-label {
    font-size: 16px;
    font-weight: bold;
    margin-right: 8px;
  }
  
  .payable-value {
    font-size: 18px;
    font-weight: bold;
    color: #f00;
    text-decoration: underline;
    text-decoration-color: #f00;
  }
}
</style>
