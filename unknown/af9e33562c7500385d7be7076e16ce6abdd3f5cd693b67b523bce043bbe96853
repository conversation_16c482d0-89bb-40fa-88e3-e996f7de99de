<template>
  <n-modal
    v-model:show="modalVisible"
    :title="isEdit ? '编辑启票' : '新增启票'"
    preset="card"
    :style="isMaximized ? { width: '90%', height: '90%' } : { width: '50vw', minWidth: '800px' }"
    :mask-closable="false"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component :is="isMaximized ? ContractOutline : ExpandOutline" />
          </n-icon>
        </template>
      </n-button>
    </template>
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="4" :x-gap="16" :y-gap="16">
        <n-grid-item>
          <n-form-item label="VIN" path="vin">
            <n-input v-model:value="form.vin" placeholder="请输入VIN" />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="启票日期" path="erpOrderDate">
            <n-date-picker
              v-model:value="dateValue"
              type="date"
              clearable
              style="width: 100%"
              @update:value="handleDateChange"
              value-format="yyyy-MM-dd"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="启票金额(元)" path="startBillPrice">
            <n-input-number
              v-model:value="form.startBillPrice"
              placeholder="请输入启票金额"
              style="width: 100%"
              :min="0"
              :precision="2"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="车辆类别" path="vehicleCategory">
            <n-input v-model:value="form.vehicleCategory" placeholder="请输入车辆类别" />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="车型系列" path="vehicleSeries">
            <n-input v-model:value="form.vehicleSeries" placeholder="请输入车型系列" />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="车型代码" path="vehicleModelCode">
            <n-input v-model:value="form.vehicleModelCode" placeholder="请输入车型代码" />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="车型名称" path="vehicleModelName">
            <n-input v-model:value="form.vehicleModelName" placeholder="请输入车型名称" />
          </n-form-item>
        </n-grid-item>
        <n-grid-item span="2">
          <n-form-item label="启票单位" path="invoiceOrgName">
            <department-selector
              v-model="selectedOrg"
              mode="single"
              label="选择启票单位"
              width="100%"
              @update:model-value="handleOrgChange"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item span="2">
          <n-form-item label="采购单位" path="purchaseOrgName">
            <department-selector
              v-model="selectedPurchaseOrg"
              mode="single"
              label="选择采购单位"
              width="100%"
              @update:model-value="handlePurchaseOrgChange"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item span="2">
          <n-form-item label="收车单位" path="receiveOrgName">
            <department-selector
              v-model="selectedReceiveOrg"
              mode="single"
              label="选择收车单位"
              width="100%"
              @update:model-value="handleReceiveOrgChange"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item span="4">
          <n-form-item label="发运地址" path="investmentEntity">
            <n-input v-model:value="form.investmentEntity" placeholder="请输入发运地址" />
          </n-form-item>
        </n-grid-item>
      </n-grid>
      <n-form-item label="付款备注" path="paymentRemark">
        <n-input v-model:value="form.paymentRemark" type="textarea" placeholder="请输入付款备注信息" />
      </n-form-item>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="closeModal">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="loading">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ContractOutline, ExpandOutline } from '@vicons/ionicons5'
import startBillApi from '@/api/startBill'
import messages from '@/utils/messages'
import DepartmentSelector from '@/components/users/DepartmentSelector.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 状态变量
// 使用简单的ref而不是computed属性，避免可能的循环引用
const modalVisible = ref(false)

// 监听props.visible的变化
watch(() => props.visible, (newVal) => {
  modalVisible.value = newVal
})

// 监听modalVisible的变化
watch(modalVisible, (newVal) => {
  if (newVal !== props.visible) {
    emit('update:visible', newVal)
  }
})
const isMaximized = ref(false)
const loading = ref(false)
const formRef = ref(null)
const selectedOrg = ref(null)
const selectedPurchaseOrg = ref(null)
const selectedReceiveOrg = ref(null)
const dateValue = ref(null)

// 表单数据
const form = reactive({
  id: null,
  erpOrderDate: '',
  invoiceOrgName: '',
  purchaseOrgName: '', // 采购单位
  receiveOrgName: '', // 收车单位
  vehicleCategory: '',
  vehicleSeries: '',
  vehicleModelCode: '',
  vehicleModelName: '',
  vin: '',
  startBillPrice: null,
  investmentEntity: '',
  paymentRemark: ''
})

// 表单验证规则
const rules = {
  erpOrderDate: {
    required: true,
    message: '请选择启票日期',
    trigger: ['blur', 'change'],
    validator: (_, value) => {
      if (!value) {
        return new Error('请选择启票日期')
      }
      return true
    }
  },
  invoiceOrgName: {
    required: true,
    message: '请选择启票单位',
    trigger: ['blur', 'input']
  },
  vin: {
    required: true,
    message: '请输入VIN码',
    trigger: ['blur', 'input']
  },
  startBillPrice: {
    required: true,
    message: '请输入启票金额',
    trigger: ['blur', 'change'],
    type: 'number', // 确保金额是数字类型
    validator: (_, value) => {
      if (value === null || value === undefined || value === '') {
        return new Error('请输入启票金额')
      }
      if (typeof value !== 'number') {
        return new Error('启票金额必须是数字')
      }
      if (value <= 0) {
        return new Error('启票金额必须大于0')
      }
      return true
    }
  }
}

// 切换弹窗最大化/最小化
const toggleSize = () => {
  isMaximized.value = !isMaximized.value
}

// 关闭弹窗
const closeModal = () => {
  modalVisible.value = false
}

// 处理机构选择变化
const handleOrgChange = (org) => {
  if (org) {
    form.invoiceOrgName = org.name
  } else {
    form.invoiceOrgName = ''
  }
}

// 处理采购单位选择变化
const handlePurchaseOrgChange = (org) => {
  if (org) {
    form.purchaseOrgName = org.name
  } else {
    form.purchaseOrgName = ''
  }
}

// 处理收车单位选择变化
const handleReceiveOrgChange = (org) => {
  if (org) {
    form.receiveOrgName = org.name
  } else {
    form.receiveOrgName = ''
  }
}

// 处理日期变化
const handleDateChange = (date) => {
  console.log('日期变化:', date, typeof date)
  if (!date) {
    form.erpOrderDate = ''
    return
  }

  try {
    let formattedDate = ''

    // 检查日期类型并相应处理
    if (typeof date === 'string') {
      // 如果已经是字符串格式，检查是否符合 yyyy-MM-dd 格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        formattedDate = date
      } else {
        // 尝试解析其他格式的日期字符串
        const parsedDate = new Date(date)
        if (!isNaN(parsedDate.getTime())) {
          const year = parsedDate.getFullYear()
          const month = String(parsedDate.getMonth() + 1).padStart(2, '0')
          const day = String(parsedDate.getDate()).padStart(2, '0')
          formattedDate = `${year}-${month}-${day}`
        }
      }
    } else if (date instanceof Date) {
      // 如果是Date对象
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      formattedDate = `${year}-${month}-${day}`
    } else if (typeof date === 'number') {
      // 如果是时间戳
      const dateObj = new Date(date)
      const year = dateObj.getFullYear()
      const month = String(dateObj.getMonth() + 1).padStart(2, '0')
      const day = String(dateObj.getDate()).padStart(2, '0')
      formattedDate = `${year}-${month}-${day}`
    } else if (Array.isArray(date) && date.length >= 3) {
      // 如果是数组 [year, month, day]
      const [year, month, day] = date
      formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
    }

    // 更新表单值
    form.erpOrderDate = formattedDate
    console.log('格式化后的日期:', formattedDate)
  } catch (error) {
    console.error('日期处理错误:', error)
    form.erpOrderDate = ''
  }
}

// 处理保存
const handleSave = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return

    try {
      loading.value = true

      // 准备请求数据
      const data = {
        erpOrderDate: form.erpOrderDate || '', // 使用空字符串而不是null
        invoiceOrgName: form.invoiceOrgName,
        purchaseOrgName: form.purchaseOrgName,
        receiveOrgName: form.receiveOrgName,
        vehicleCategory: form.vehicleCategory,
        vehicleSeries: form.vehicleSeries,
        vehicleModelCode: form.vehicleModelCode,
        vehicleModelName: form.vehicleModelName,
        vin: form.vin,
        startBillPrice: form.startBillPrice ? Number(form.startBillPrice) : null, // 确保金额是数字类型
        shippingAddress: form.investmentEntity, // 将investmentEntity映射到shippingAddress
        paymentRemark: form.paymentRemark || ''
      }

      // 如果是编辑模式，添加ID
      if (props.isEdit) {
        data.id = form.id
      }

      // 调用保存API
      const response = props.isEdit
        ? await startBillApi.updateStartBill(data)
        : await startBillApi.addStartBill(data)

      if (response.code === 200) {
        messages.success(props.isEdit ? '更新成功' : '添加成功')
        modalVisible.value = false
        // 通知父组件保存成功
        emit('success')
      } else {
        messages.error(response.message || (props.isEdit ? '更新失败' : '添加失败'))
      }
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      loading.value = false
    }
  })
}

// 初始化表单数据
const initFormData = () => {
  // 重置表单
  Object.assign(form, {
    id: null,
    erpOrderDate: '',
    invoiceOrgName: '',
    purchaseOrgName: '',
    receiveOrgName: '',
    vehicleCategory: '',
    vehicleSeries: '',
    vehicleModelCode: '',
    vehicleModelName: '',
    vin: '',
    startBillPrice: null,
    investmentEntity: '',
    paymentRemark: ''
  })

  // 重置日期选择器的值
  dateValue.value = null
  selectedOrg.value = null
  selectedPurchaseOrg.value = null
  selectedReceiveOrg.value = null

  // 如果是编辑模式，填充表单数据
  if (props.isEdit && props.editData) {
    // 使用解构赋值来避免直接引用可能包含循环引用的对象
    const {
      id,
      erpOrderDate,
      invoiceOrgName,
      purchaseOrgName,
      receiveOrgName,
      vehicleCategory,
      vehicleSeries,
      vehicleModelCode,
      vehicleModelName,
      vin,
      startBillPrice,
      investmentEntity,
      shippingAddress, // 添加shippingAddress字段
      paymentRemark,
      invoiceOrgId,
      purchaseOrgId,
      receiveOrgId
    } = props.editData;

    Object.assign(form, {
      id,
      erpOrderDate: erpOrderDate || '', // 使用空字符串而不是null
      invoiceOrgName: invoiceOrgName || '',
      purchaseOrgName: purchaseOrgName || '',
      receiveOrgName: receiveOrgName || '',
      vehicleCategory: vehicleCategory || '',
      vehicleSeries: vehicleSeries || '',
      vehicleModelCode: vehicleModelCode || '',
      vehicleModelName: vehicleModelName || '',
      vin: vin || '',
      startBillPrice,
      // 优先使用shippingAddress字段，如果不存在则使用investmentEntity字段
      investmentEntity: shippingAddress || investmentEntity || '',
      paymentRemark: paymentRemark || ''
    })

    // 如果有日期，设置日期选择器的值
    if (erpOrderDate) {
      try {
        // 尝试将日期字符串转换为日期对象
        const [year, month, day] = erpOrderDate.split('-').map(Number)
        dateValue.value = new Date(year, month - 1, day)
      } catch (error) {
        console.error('日期转换错误:', error)
        dateValue.value = null
      }
    } else {
      dateValue.value = null
    }

    // 如果有启票单位，设置选择器的值
    if (invoiceOrgName) {
      selectedOrg.value = {
        id: invoiceOrgId || '',
        name: invoiceOrgName
      }
    }

    // 如果有采购单位，设置选择器的值
    if (purchaseOrgName) {
      selectedPurchaseOrg.value = {
        id: purchaseOrgId || '',
        name: purchaseOrgName
      }
    }

    // 如果有收车单位，设置选择器的值
    if (receiveOrgName) {
      selectedReceiveOrg.value = {
        id: receiveOrgId || '',
        name: receiveOrgName
      }
    }
  }
}

// 监听弹窗显示状态变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initFormData()
  }
})

// 监听编辑数据变化
watch(() => props.isEdit, (newVal) => {
  if (props.visible && newVal) {
    initFormData()
  }
})
</script>

<style scoped>
/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :deep(.department-selector) {
    width: 100% !important;
  }
}
</style>
