<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    :title="title"
    preset="card"
    style="width: 80%; max-width: 1200px;"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
      size="medium"
      label-width="auto"
    >
      <!-- 客户信息部分 -->
      <customer-info-section
        :form="form"
        @show-customer-selector="customerSelectorVisible = true"
      />

      <!-- 订单备注部分 -->
      <remark-section :form="form" />
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="saving">确定</n-button>
      </n-space>
    </template>

    <!-- 客户选择器 -->
    <customer-selector v-model:visible="customerSelectorVisible" @select="handleCustomerSelected" />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { NModal, NForm, NButton, NSpace } from 'naive-ui'
import messages from '@/utils/messages'
import vehicleOrderApi from '@/api/vehicleOrder'
import CustomerSelector from '@/components/customer/CustomerSelector.vue'
import CustomerInfoSection from '@/components/orders/sections/CustomerInfoSection.vue'
import RemarkSection from '@/components/orders/sections/RemarkSection.vue'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增定金订单'
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['update:visible', 'save', 'cancel'])

// 表单引用
const formRef = ref(null)

// 不再需要定金状态选项

// 客户选择器可见性
const customerSelectorVisible = ref(false)

// 保存中状态
const saving = ref(false)

// 表单数据
const form = reactive({
  id: null,
  customerId: null,
  customerName: '',
  customerPhone: '',
  customerAddress: '',
  salesperson: '',
  invoiceOrgId: null,
  invoiceOrgName: '',
  exclusiveDiscountType: null,
  exclusiveDiscountAmount: null,
  paymentRemark: ''
})

// 表单验证规则
const rules = {
  customerName: {
    required: true,
    message: '请选择客户',
    trigger: 'blur'
  }
}

// 模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 更新可见性
const updateVisible = (value) => {
  if (!value) {
    emit('cancel')
  }
}

// 处理客户选择
const handleCustomerSelected = (customer) => {
  messages.success(`已选择客户: ${customer.customerName}`)

  // 更新客户信息
  form.customerId = customer.id
  form.customerName = customer.customerName
  form.customerPhone = customer.mobile
  form.customerAddress = customer.address
  form.salesperson = customer.ownerSellerName

  // 设置销售顾问和销售主管ID
  form.salesAgentId = customer.ownerSellerId || customer.id
  form.salesLeaderId = customer.ownerLeaderId || customer.id

  // 如果是新增，也更新销售单位
  if (!props.isEdit) {
    form.invoiceOrgId = customer.ownerOrgId || null
    form.invoiceOrgName = customer.ownerOrgName || ''
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    customerId: null,
    customerName: '',
    customerPhone: '',
    customerAddress: '',
    salesperson: '',
    invoiceOrgId: null,
    invoiceOrgName: '',
    exclusiveDiscountType: null,
    exclusiveDiscountAmount: null,
    paymentRemark: ''
  })
}

// 设置表单数据
const setFormData = (data) => {
  if (!data) return

  Object.assign(form, {
    id: data.id,
    customerId: data.customerId,
    customerName: data.customerName,
    customerPhone: data.customerPhone || data.mobile,
    customerAddress: data.customerAddress || data.address,
    salesperson: data.salesperson || data.salesAgentName,
    invoiceOrgId: data.invoiceOrgId || data.salesOrgId,
    invoiceOrgName: data.invoiceOrgName || data.salesOrgName,
    exclusiveDiscountType: data.exclusiveDiscountType,
    exclusiveDiscountAmount: data.exclusiveDiscountAmount ? data.exclusiveDiscountAmount / 100 : null, // 分转元
    paymentRemark: data.paymentRemark || ''
  })
}

// 处理取消
const handleCancel = () => {
  modelVisible.value = false
  emit('cancel')
}

// 处理保存
const handleSave = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) {
      return
    }

    saving.value = true
    try {
      // 准备提交的数据
      const submitData = {
        ...form,
        // 将金额从元转为分
        exclusiveDiscountAmount: form.exclusiveDiscountAmount ? Math.round(form.exclusiveDiscountAmount * 100) : 0,
        orderType: 'deposit' // 标记为定金订单
      }

      // 调用API保存数据
      const response = props.isEdit
        ? await vehicleOrderApi.updateDepositOrder(form.id, submitData)
        : await vehicleOrderApi.createDepositOrder(submitData)

      if (response.code === 200) {
        messages.success(props.isEdit ? '定金订单更新成功' : '定金订单创建成功')
        modelVisible.value = false
        emit('save')
      } else {
        messages.error(response.message || (props.isEdit ? '更新失败' : '创建失败'))
      }
    } catch (error) {
      console.error('保存定金订单失败:', error)
      messages.error('操作失败，请稍后重试')
    } finally {
      saving.value = false
    }
  })
}

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData
})
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color, #18a058);
}

.title-button {
  margin-left: 12px;
}
</style>
