<template>
  <div class="gift-stock-page-new">
    <biz-org-list-layout
      title="赠品库存"
      search-placeholder="请输入赠品名称或规格"
      :columns="columns"
      :api-service="giftStockApi"
      org-id-field="stockOrgId"
      row-key="id"
      :default-new-row="defaultNewRow"
      @select-org="handleOrgSelect"
      @add-data="handleAddData"
      @edit-data="handleEditData"
      @save-data="handleSaveData"
      @delete-data="handleDeleteData"
    />
  </div>
</template>

<script setup>
import BizOrgListLayout from "@/components/layout/BizOrgListLayout.vue";
import { useGiftStockPageNew } from "./GiftStockPageNew.js";

// 定义emit函数
const emit = defineEmits([
  "select-org",
  "add-data",
  "edit-data",
  "save-data",
  "cancel-edit",
  "delete-data",
  "refresh-data",
]);

// 使用组合式函数获取所有需要的数据和方法
const {
  giftStockApi,
  defaultNewRow,
  createColumns,
  handleOrgSelect,
  handleAddData,
  handleEditData,
  handleSaveData,
  handleDeleteData,
} = useGiftStockPageNew();

// 创建表格列定义，传入emit函数
const columns = createColumns(emit);
</script>

<style lang="scss">
@use "./styles/gift-stock-page";
</style>
