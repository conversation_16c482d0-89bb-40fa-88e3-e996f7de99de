<template>
  <div class="analytics-page">
    <!-- 筛选区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <!-- 统计日期 -->
        <div class="filter-row">
          <div class="filter-label">统计日期</div>
          <div class="filter-options">
            <n-radio-group v-model:value="timeRange" @update:value="handleTimeRangeChange" class="custom-radio-group">
              <n-radio-button v-for="option in filteredDateRangeOptions" :key="option.value" :value="option.value"
                class="custom-radio-button">
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <!-- 在售品牌 -->
        <div class="filter-row">
          <div class="filter-label">在售品牌</div>
          <div class="filter-options">
            <n-radio-group v-model:value="selectedBrand" @update:value="handleBrandChange" class="custom-radio-group">
              <n-radio-button :value="null" class="custom-radio-button">不限</n-radio-button>
              <n-radio-button v-for="option in brandOptions" :key="option.value" :value="option.value"
                class="custom-radio-button">
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
      </div>

      <!-- 统计机构 -->
      <div class="filter-row">
        <div class="filter-label">统计机构</div>
        <div class="filter-options">
          <department-selector v-model="selectedOrg" mode="single" label="选择统计机构" :width="'100%'"
            style="min-width: 200px; max-width: 300px;" @update:model-value="handleOrgChange" />
        </div>
      </div>

    </n-card>

    <!-- 工具栏 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon>
              <RefreshOutline />
            </n-icon>
          </template>
          刷新数据
        </n-button>
      </n-space>
    </n-space>

    <!-- 统计卡片 - 第一行 -->
    <n-grid :cols="4" :x-gap="16" class="stat-cards">
      <n-grid-item>
        <n-card size="small" class="stat-card">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#2080f0">
                <DocumentTextOutline />
              </n-icon>
              <span>启票数量</span>
            </n-space>
          </template>
          <div class="stat-value">{{ formatNumber(startBillCount) }}</div>
          <div class="stat-compare">
            <span>环比</span>
            <span :class="startBillCountCompare >= 0 ? 'up' : 'down'">
              {{ startBillCountCompare >= 0 ? '+' : '' }}{{ startBillCountCompare }}%
            </span>
            <span style="margin-left: 10px;">同比</span>
            <span :class="startBillCountYearCompare >= 0 ? 'up' : 'down'">
              {{ startBillCountYearCompare >= 0 ? '+' : '' }}{{ startBillCountYearCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small" class="stat-card">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#d03050">
                <CartOutline />
              </n-icon>
              <span>销售数量</span>
            </n-space>
          </template>
          <div class="stat-value">{{ formatNumber(salesCount) }}</div>
          <div class="stat-compare">
            <span>环比</span>
            <span :class="salesCountCompare >= 0 ? 'up' : 'down'">
              {{ salesCountCompare >= 0 ? '+' : '' }}{{ salesCountCompare }}%
            </span>
            <span style="margin-left: 10px;">同比</span>
            <span :class="salesCountYearCompare >= 0 ? 'up' : 'down'">
              {{ salesCountYearCompare >= 0 ? '+' : '' }}{{ salesCountYearCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small" class="stat-card">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#18a058">
                <ArchiveOutline />
              </n-icon>
              <span>库存数量</span>
            </n-space>
          </template>
          <div class="stat-value">{{ formatNumber(inventoryCount) }}</div>
          <div class="stat-compare">
            <span>环比</span>
            <span :class="inventoryCountCompare >= 0 ? 'up' : 'down'">
              {{ inventoryCountCompare >= 0 ? '+' : '' }}{{ inventoryCountCompare }}%
            </span>
            <span style="margin-left: 10px;">同比</span>
            <span :class="inventoryCountYearCompare >= 0 ? 'up' : 'down'">
              {{ inventoryCountYearCompare >= 0 ? '+' : '' }}{{ inventoryCountYearCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small" class="stat-card">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#f0a020">
                <SyncOutline />
              </n-icon>
              <span>销售毛利率</span>
            </n-space>
          </template>
          <div class="stat-value">{{ inventoryTurnoverRate }}%</div>
          <div class="stat-compare">
            <span>环比</span>
            <span :class="inventoryTurnoverRateCompare >= 0 ? 'up' : 'down'">
              {{ inventoryTurnoverRateCompare >= 0 ? '+' : '' }}{{ inventoryTurnoverRateCompare }}%
            </span>
            <span style="margin-left: 10px;">同比</span>
            <span :class="inventoryTurnoverRateYearCompare >= 0 ? 'up' : 'down'">
              {{ inventoryTurnoverRateYearCompare >= 0 ? '+' : '' }}{{ inventoryTurnoverRateYearCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 统计卡片 - 第二行 -->
    <n-grid :cols="4" :x-gap="16" class="stat-cards">
      <n-grid-item>
        <n-card size="small" class="stat-card">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#2080f0">
                <DocumentTextOutline />
              </n-icon>
              <span>启票金额</span>
            </n-space>
          </template>
          <div class="stat-value">
            ¥{{ formatNumber(startBillAmount) }}
            <span class="amount-in-wan">{{ formatAmountInWan(startBillAmount) }}万</span>
          </div>
          <div class="stat-compare">
            <span>环比</span>
            <span :class="startBillAmountCompare >= 0 ? 'up' : 'down'">
              {{ startBillAmountCompare >= 0 ? '+' : '' }}{{ startBillAmountCompare }}%
            </span>
            <span style="margin-left: 10px;">同比</span>
            <span :class="startBillAmountYearCompare >= 0 ? 'up' : 'down'">
              {{ startBillAmountYearCompare >= 0 ? '+' : '' }}{{ startBillAmountYearCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small" class="stat-card">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#d03050">
                <WalletOutline />
              </n-icon>
              <span>销售金额</span>
            </n-space>
          </template>
          <div class="stat-value">
            ¥{{ formatNumber(totalSales) }}
            <span class="amount-in-wan">{{ formatAmountInWan(totalSales) }}万</span>
          </div>
          <div class="stat-compare">
            <span>环比</span>
            <span :class="salesCompare >= 0 ? 'up' : 'down'">
              {{ salesCompare >= 0 ? '+' : '' }}{{ salesCompare }}%
            </span>
            <span style="margin-left: 10px;">同比</span>
            <span :class="salesYearCompare >= 0 ? 'up' : 'down'">
              {{ salesYearCompare >= 0 ? '+' : '' }}{{ salesYearCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small" class="stat-card">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#18a058">
                <BagHandleOutline />
              </n-icon>
              <span>库存金额</span>
            </n-space>
          </template>
          <div class="stat-value">
            ¥{{ formatNumber(totalInventory) }}
            <span class="amount-in-wan">{{ formatAmountInWan(totalInventory) }}万</span>
          </div>
          <div class="stat-compare">
            <span>环比</span>
            <span :class="inventoryCompare >= 0 ? 'up' : 'down'">
              {{ inventoryCompare >= 0 ? '+' : '' }}{{ inventoryCompare }}%
            </span>
            <span style="margin-left: 10px;">同比</span>
            <span :class="inventoryYearCompare >= 0 ? 'up' : 'down'">
              {{ inventoryYearCompare >= 0 ? '+' : '' }}{{ inventoryYearCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small" class="stat-card">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#f0a020">
                <TrendingUpOutline />
              </n-icon>
              <span>销售毛利润</span>
            </n-space>
          </template>
          <div class="stat-value">
            ¥{{ formatNumber(grossProfit) }}
            <span class="amount-in-wan">{{ formatAmountInWan(grossProfit) }}万</span>
          </div>
          <div class="stat-compare">
            <span>环比</span>
            <span :class="inventoryCompare >= 0 ? 'up' : 'down'">
              {{ inventoryCompare >= 0 ? '+' : '' }}{{ inventoryCompare }}%
            </span>
            <span style="margin-left: 10px;">同比</span>
            <span :class="profitYearCompare >= 0 ? 'up' : 'down'">
              {{ profitYearCompare >= 0 ? '+' : '' }}{{ profitYearCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 图表区域 -->
    <n-grid :cols="2" :x-gap="16" :y-gap="16" class="chart-container">
      <n-grid-item>
        <n-card title="销售趋势" size="small">
          <div ref="salesTrendChart" class="chart"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="启票趋势" size="small">
          <div ref="purchaseTrendChart" class="chart"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="品牌销售占比" size="small">
          <div ref="productSalesChart" class="chart"></div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card title="库存分布" size="small">
          <div ref="inventoryDistributionChart" class="chart"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 数据表格 -->
    <n-card title="品牌销售排行" class="data-table-card">
      <n-tabs type="line">
        <n-tab-pane name="sales" tab="销售额排行">
          <n-data-table :columns="salesRankColumns" :data="salesRankData" :pagination="pagination" :bordered="false" />
        </n-tab-pane>
        <n-tab-pane name="quantity" tab="销售量排行">
          <n-data-table :columns="quantityRankColumns" :data="quantityRankData" :pagination="pagination"
            :bordered="false" />
        </n-tab-pane>
        <n-tab-pane name="profit" tab="利润排行">
          <n-data-table :columns="profitRankColumns" :data="profitRankData" :pagination="pagination"
            :bordered="false" />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, h, reactive, onBeforeUnmount, computed } from 'vue'
import { useMessage } from 'naive-ui'
import {
  RefreshOutline, CartOutline, BagHandleOutline,
  WalletOutline, TrendingUpOutline, DocumentTextOutline,
  ArchiveOutline, SyncOutline
} from '@vicons/ionicons5'
import {
  NButton, NSpace, NIcon, NDataTable, NGrid, NGridItem, NCard,
  NTabs, NTabPane, NProgress, NRadioGroup, NRadioButton
} from 'naive-ui'
import * as echarts from 'echarts'
import { dateRangeOptions } from '@/utils/dateRange'
import DepartmentSelector from '@/components/users/DepartmentSelector.vue'
import { getDictOptions } from '@/api/dict'

// 状态变量
const loading = ref(false)
const message = useMessage()

// 筛选表单
const timeRange = ref('thisMonth')
const selectedOrg = ref(null)
const selectedBrand = ref(null)
const brandOptions = ref([])

// 过滤掉自定义选项的日期范围选项
const filteredDateRangeOptions = computed(() => {
  return dateRangeOptions.filter(option => option.value !== 'custom')
})

// 图表引用
const salesTrendChart = ref(null)
const purchaseTrendChart = ref(null)
const productSalesChart = ref(null)
const inventoryDistributionChart = ref(null)

// 图表实例
let salesTrendChartInstance = null
let purchaseTrendChartInstance = null
let productSalesChartInstance = null
let inventoryDistributionChartInstance = null

// 统计数据
// 第一行卡片数据
const startBillCount = ref(125)
const startBillCountCompare = ref(15.2)
const startBillCountYearCompare = ref(22.5)
const salesCount = ref(98)
const salesCountCompare = ref(8.7)
const salesCountYearCompare = ref(18.3)
const inventoryCount = ref(230)
const inventoryCountCompare = ref(-3.5)
const inventoryCountYearCompare = ref(5.8)
const inventoryTurnoverRate = ref(12.8)
const inventoryTurnoverRateCompare = ref(2.3)
const inventoryTurnoverRateYearCompare = ref(4.2)

// 第二行卡片数据
const startBillAmount = ref(285460.50)
const startBillAmountCompare = ref(10.8)
const startBillAmountYearCompare = ref(25.6)
const totalSales = ref(358620.75)
const salesCompare = ref(12.5)
const salesYearCompare = ref(28.7)
const totalInventory = ref(520350.80)
const inventoryCompare = ref(3.8)
const inventoryYearCompare = ref(15.2)
const grossProfit = ref(102140.45)
const profitRate = ref(28.5)
const profitYearCompare = ref(32.1)

// 保留这些变量以便图表使用
const totalPurchase = ref(256480.30)
const purchaseCompare = ref(-5.2)

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 5,
  showSizePicker: false,
  pageSizes: [5, 10],
  onChange: (page) => {
    pagination.page = page
  }
})

// 销售排行表格列
const salesRankColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    render(row) {
      const colors = ['#f5a623', '#8b572a', '#7ed321']
      return h('div', { class: 'rank-cell' }, [
        row.rank <= 3
          ? h('div', { class: 'rank-medal', style: { backgroundColor: colors[row.rank - 1] } }, row.rank)
          : h('div', { class: 'rank-number' }, row.rank)
      ])
    }
  },
  { title: '商品名称', key: 'productName' },
  { title: '商品类别', key: 'category' },
  {
    title: '销售额',
    key: 'salesAmount',
    render(row) {
      return h('span', { style: { fontWeight: 'bold' } }, `¥${row.salesAmount.toLocaleString()}`)
    }
  },
  {
    title: '销售占比',
    key: 'percentage',
    render(row) {
      return h(NProgress, {
        type: 'line',
        percentage: row.percentage,
        indicatorPlacement: 'inside',
        processing: true,
        height: 15,
        color: '#2080f0'
      })
    }
  }
]

// 销售量排行表格列
const quantityRankColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    render(row) {
      const colors = ['#f5a623', '#8b572a', '#7ed321']
      return h('div', { class: 'rank-cell' }, [
        row.rank <= 3
          ? h('div', { class: 'rank-medal', style: { backgroundColor: colors[row.rank - 1] } }, row.rank)
          : h('div', { class: 'rank-number' }, row.rank)
      ])
    }
  },
  { title: '商品名称', key: 'productName' },
  { title: '商品类别', key: 'category' },
  {
    title: '销售数量',
    key: 'quantity',
    render(row) {
      return h('span', { style: { fontWeight: 'bold' } }, row.quantity.toLocaleString())
    }
  },
  {
    title: '销售占比',
    key: 'percentage',
    render(row) {
      return h(NProgress, {
        type: 'line',
        percentage: row.percentage,
        indicatorPlacement: 'inside',
        processing: true,
        height: 15,
        color: '#18a058'
      })
    }
  }
]

// 利润排行表格列
const profitRankColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    render(row) {
      const colors = ['#f5a623', '#8b572a', '#7ed321']
      return h('div', { class: 'rank-cell' }, [
        row.rank <= 3
          ? h('div', { class: 'rank-medal', style: { backgroundColor: colors[row.rank - 1] } }, row.rank)
          : h('div', { class: 'rank-number' }, row.rank)
      ])
    }
  },
  { title: '商品名称', key: 'productName' },
  { title: '商品类别', key: 'category' },
  {
    title: '销售额',
    key: 'salesAmount',
    render(row) {
      return h('span', null, `¥${row.salesAmount.toLocaleString()}`)
    }
  },
  {
    title: '利润',
    key: 'profit',
    render(row) {
      return h('span', { style: { fontWeight: 'bold', color: '#18a058' } }, `¥${row.profit.toLocaleString()}`)
    }
  },
  {
    title: '利润率',
    key: 'profitRate',
    render(row) {
      const color = row.profitRate >= 30 ? '#18a058' : row.profitRate >= 15 ? '#2080f0' : '#d03050'
      return h(NProgress, {
        type: 'line',
        percentage: row.profitRate,
        indicatorPlacement: 'inside',
        processing: true,
        height: 15,
        color
      })
    }
  }
]

// 模拟数据 - 销售额排行
const salesRankData = [
  { rank: 1, productName: '商品A', category: '电子产品', salesAmount: 125680, percentage: 35 },
  { rank: 2, productName: '商品B', category: '家居用品', salesAmount: 98450, percentage: 27.5 },
  { rank: 3, productName: '商品C', category: '食品饮料', salesAmount: 56780, percentage: 15.8 },
  { rank: 4, productName: '商品D', category: '服装鞋帽', salesAmount: 45320, percentage: 12.6 },
  { rank: 5, productName: '商品E', category: '办公用品', salesAmount: 32390, percentage: 9.1 }
]

// 模拟数据 - 销售量排行
const quantityRankData = [
  { rank: 1, productName: '商品C', category: '食品饮料', quantity: 5680, percentage: 42.3 },
  { rank: 2, productName: '商品E', category: '办公用品', quantity: 3240, percentage: 24.1 },
  { rank: 3, productName: '商品A', category: '电子产品', quantity: 1890, percentage: 14.1 },
  { rank: 4, productName: '商品D', category: '服装鞋帽', quantity: 1560, percentage: 11.6 },
  { rank: 5, productName: '商品B', category: '家居用品', quantity: 1050, percentage: 7.9 }
]

// 模拟数据 - 利润排行
const profitRankData = [
  { rank: 1, productName: '商品A', category: '电子产品', salesAmount: 125680, profit: 45680, profitRate: 36.3 },
  { rank: 2, productName: '商品B', category: '家居用品', salesAmount: 98450, profit: 28450, profitRate: 28.9 },
  { rank: 3, productName: '商品D', category: '服装鞋帽', salesAmount: 45320, profit: 12320, profitRate: 27.2 },
  { rank: 4, productName: '商品C', category: '食品饮料', salesAmount: 56780, profit: 9780, profitRate: 17.2 },
  { rank: 5, productName: '商品E', category: '办公用品', salesAmount: 32390, profit: 5910, profitRate: 18.2 }
]

// 页面加载时初始化图表和加载品牌选项
onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
  fetchBrandOptions()
})

// 页面卸载时销毁图表实例
onBeforeUnmount(() => {
  destroyCharts()
  window.removeEventListener('resize', handleResize)
})

// 初始化所有图表
const initCharts = () => {
  // 初始化销售趋势图表
  salesTrendChartInstance = echarts.init(salesTrendChart.value)
  salesTrendChartInstance.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: [25680, 28450, 32780, 36540, 42180, 38920, 45680, 48920, 52340, 49870, 45680, 58450],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#2080f0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#5470c6' },
              { offset: 0.7, color: '#3c5fdd' },
              { offset: 1, color: '#2851e7' }
            ])
          }
        }
      },
      {
        name: '同比',
        type: 'line',
        data: [22450, 25680, 28920, 32450, 38920, 35680, 42180, 45680, 48920, 46540, 42180, 52340],
        itemStyle: {
          color: '#f0a020'
        }
      }
    ]
  })

  // 初始化采购趋势图表
  purchaseTrendChartInstance = echarts.init(purchaseTrendChart.value)
  purchaseTrendChartInstance.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '采购额',
        type: 'bar',
        data: [18450, 22180, 25680, 28920, 32450, 30180, 35680, 38920, 42180, 39870, 36540, 45680],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#f6a3a3' },
            { offset: 0.5, color: '#e86767' },
            { offset: 1, color: '#d03050' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#e86767' },
              { offset: 0.7, color: '#d03050' },
              { offset: 1, color: '#b71c3b' }
            ])
          }
        }
      },
      {
        name: '同比',
        type: 'line',
        data: [16780, 19870, 22450, 25680, 28920, 27450, 32180, 35680, 38920, 36540, 33920, 42180],
        itemStyle: {
          color: '#18a058'
        }
      }
    ]
  })

  // 初始化商品销售占比图表
  productSalesChartInstance = echarts.init(productSalesChart.value)

  // 使用品牌选项作为图例数据
  const brandNames = brandOptions.value.length > 0
    ? brandOptions.value.map(item => item.label)
    : ['引力', '启源', '凯程', '深蓝汽车', '阿维塔'] // 默认值，将在品牌选项加载后更新

  // 生成模拟销售数据
  const salesData = brandNames.map((name, index) => {
    // 生成一些随机销售数据
    const baseValues = [125680, 98450, 56780, 45320, 32390]
    const value = baseValues[index] || Math.floor(Math.random() * 100000 + 30000)
    return { value, name }
  })

  productSalesChartInstance.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: brandNames
    },
    series: [
      {
        name: '销售额',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: salesData
      }
    ]
  })

  // 初始化库存分布图表
  inventoryDistributionChartInstance = echarts.init(inventoryDistributionChart.value)

  // 使用品牌选项作为x轴数据
  const brandLabels = brandOptions.value.length > 0
    ? brandOptions.value.map(item => item.label)
    : ['引力', '启源', '凯程', '深蓝汽车', '阿维塔'] // 默认值，将在品牌选项加载后更新

  // 生成模拟库存数据
  const inventoryCountData = brandLabels.map((_, index) => {
    // 生成一些随机库存数量数据
    const baseValues = [1250, 1850, 3560, 2180, 2950]
    return baseValues[index] || Math.floor(Math.random() * 3000 + 1000)
  })

  const inventoryAmountData = brandLabels.map((_, index) => {
    // 生成一些随机库存金额数据
    const baseValues = [185680, 125450, 85680, 95320, 78450]
    return baseValues[index] || Math.floor(Math.random() * 100000 + 50000)
  })

  inventoryDistributionChartInstance.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['库存数量', '库存金额']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: brandLabels
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '金额',
        position: 'right',
        axisLabel: {
          formatter: '{value} 元'
        }
      }
    ],
    series: [
      {
        name: '库存数量',
        type: 'bar',
        data: inventoryCountData
      },
      {
        name: '库存金额',
        type: 'bar',
        yAxisIndex: 1,
        data: inventoryAmountData
      }
    ]
  })
}

// 销毁所有图表实例
const destroyCharts = () => {
  salesTrendChartInstance?.dispose()
  purchaseTrendChartInstance?.dispose()
  productSalesChartInstance?.dispose()
  inventoryDistributionChartInstance?.dispose()
}

// 处理窗口大小变化
const handleResize = () => {
  salesTrendChartInstance?.resize()
  purchaseTrendChartInstance?.resize()
  productSalesChartInstance?.resize()
  inventoryDistributionChartInstance?.resize()
}

// 刷新数据
const refreshData = () => {
  loading.value = true
  // 获取查询参数
  const params = getQueryParams()

  // 模拟API请求
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
    console.log('查询参数:', params) // 在实际应用中，这里会调用API
  }, 500)
}

// 处理时间范围变化
const handleTimeRangeChange = (value) => {
  // 这里可以根据选择的时间范围加载对应的数据
  const selectedOption = dateRangeOptions.find(item => item.value === value)
  if (selectedOption) {
    message.info(`已选择${selectedOption.label}数据`)
  }
  refreshData()
}

// 处理机构变化
const handleOrgChange = () => {
  if (selectedOrg.value) {
    message.info(`已选择${selectedOrg.value.name}`)
  }
  refreshData()
}

// 处理品牌变化
const handleBrandChange = (value) => {
  if (value) {
    message.info(`已选择${value}品牌`)
  } else {
    message.info('已选择全部品牌')
  }
  refreshData()

  // 品牌选择变化时，更新图表
  updateCharts()
}

// 获取品牌选项
const fetchBrandOptions = async () => {
  try {
    const res = await getDictOptions('vehicle_brand')
    if (res.code === 200 && res.data) {
      brandOptions.value = res.data.map(item => ({
        label: item.option_label,
        value: item.option_value
      }))
      console.log('品牌选项加载成功:', brandOptions.value)

      // 品牌选项加载成功后，更新图表
      updateCharts()
    }
  } catch (error) {
    console.error('获取品牌选项失败:', error)
    message.error('获取品牌选项失败')
  }
}

// 更新图表数据
const updateCharts = () => {
  if (!productSalesChartInstance || !inventoryDistributionChartInstance) {
    return
  }

  // 获取品牌标签
  const brandLabels = brandOptions.value.map(item => item.label)

  // 更新销售占比图表
  const salesData = brandLabels.map((name, index) => {
    // 生成一些随机销售数据
    const baseValues = [125680, 98450, 56780, 45320, 32390]
    const value = baseValues[index] || Math.floor(Math.random() * 100000 + 30000)
    return { value, name }
  })

  productSalesChartInstance.setOption({
    legend: {
      data: brandLabels
    },
    series: [
      {
        data: salesData
      }
    ]
  })

  // 更新库存分布图表
  const inventoryCountData = brandLabels.map((_, index) => {
    // 生成一些随机库存数量数据
    const baseValues = [1250, 1850, 3560, 2180, 2950]
    return baseValues[index] || Math.floor(Math.random() * 3000 + 1000)
  })

  const inventoryAmountData = brandLabels.map((_, index) => {
    // 生成一些随机库存金额数据
    const baseValues = [185680, 125450, 85680, 95320, 78450]
    return baseValues[index] || Math.floor(Math.random() * 100000 + 50000)
  })

  inventoryDistributionChartInstance.setOption({
    xAxis: {
      data: brandLabels
    },
    series: [
      {
        data: inventoryCountData
      },
      {
        data: inventoryAmountData
      }
    ]
  })
}

// 获取查询参数
const getQueryParams = () => {
  const params = {}

  // 处理日期范围
  if (timeRange.value) {
    // 将驼峰命名转换为下划线命名
    const dateScope = timeRange.value.replace(/([A-Z])/g, '_$1').toLowerCase();
    params.date_scope = dateScope
  } else {
    // 默认使用本月
    params.date_scope = 'this_month'
  }

  // 处理机构
  if (selectedOrg.value) {
    params.org_id = selectedOrg.value.id
  }

  // 处理品牌
  if (selectedBrand.value) {
    params.brand = selectedBrand.value
  }

  return params
}

// 格式化数字为千分位
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化金额为万元（保留两位小数）
const formatAmountInWan = (amount) => {
  return (amount / 10000).toFixed(2)
}
</script>

<style scoped>
.analytics-page {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.filter-card {
  margin-bottom: 16px;
}

.filter-card :deep(.n-card__content) {
  padding: 16px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
  /* 改为居中对齐 */
  flex-direction: row;
  /* 标签和选项水平排列 */
  gap: 12px;
  margin-bottom: 16px;
  width: 100%;
}

/* 由于需要在13寸MacBook Pro上也分三行显示，我们移除了原来的媒体查询 */
/* 确保筛选条件始终垂直排列，但每行内部是水平排列 */
.filter-section {
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
}

.filter-label {
  font-weight: bold;
  color: #333;
  font-size: 14px;
  min-width: 80px;
  /* 固定标签宽度 */
  flex-shrink: 0;
  /* 防止标签被压缩 */
}

.filter-options {
  display: flex;
  align-items: center;
  flex: 1;
  /* 占据剩余空间 */
  gap: 16px;
  overflow: hidden;
  /* 防止内容溢出 */
}

.custom-radio-group {
  display: flex;
  flex-wrap: nowrap;
  /* 确保选项不折叠 */
  width: 100%;
  overflow-x: auto;
  /* 如果选项太多，允许水平滚动 */
  padding-bottom: 5px;
  /* 为滚动条留出空间 */
  scrollbar-width: thin;
  /* 细滚动条 */
  -ms-overflow-style: none;
  /* IE和Edge隐藏滚动条 */
}

/* 隐藏WebKit浏览器的滚动条 */
.custom-radio-group::-webkit-scrollbar {
  height: 4px;
}

.custom-radio-group::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.custom-radio-button {
  margin-right: 8px;
  white-space: nowrap;
  /* 确保文本不换行 */
  flex-shrink: 0;
  /* 防止按钮被压缩 */
}

/* 确保筛选行和选项区域占满宽度 */
.filter-options {
  width: 100%;
}

.filter-row {
  width: 100%;
}

.custom-date-picker {
  width: 300px;
}

.toolbar {
  margin-bottom: 16px;
}

.stat-cards {
  margin-bottom: 16px;
}

.stat-card {
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card :deep(.n-card-header) {
  justify-content: flex-start;
  padding-left: 16px;
}

.stat-card :deep(.n-card__content) {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin: 16px 0;
  position: relative;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  white-space: nowrap;
  margin-left: auto;
  margin-right: auto;
}

.amount-in-wan {
  position: relative;
  margin-left: 5px;
  font-size: 12px;
  color: #999;
  font-weight: normal;
  align-self: flex-end;
  margin-bottom: 4px;
}

.stat-compare {
  font-size: 12px;
  color: #999;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.up {
  color: #18a058;
}

.normal {
  color: #2080f0;
}

.down {
  color: #d03050;
}

.chart-container {
  margin-bottom: 16px;
}

.chart {
  height: 300px;
}

.data-table-card {
  margin-bottom: 16px;
}

.rank-cell {
  display: flex;
  align-items: center;
}

.rank-medal {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.rank-number {
  font-weight: bold;
  color: #666;
}
</style>
