<template>
  <div class="purchase-page">
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增采购
        </n-button>
        <n-button type="error" @click="batchDelete" :disabled="!selectedRows.length" round>
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
      </n-space>
      <n-space>
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          clearable
          style="width: 240px"
        />
        <n-input v-model:value="searchKeyword" placeholder="输入关键词搜索" clearable>
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
        <n-button type="info" @click="handleSearch">
          搜索
        </n-button>
      </n-space>
    </n-space>

    <!-- 采购列表 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="filteredData"
      :loading="loading"
      :pagination="pagination"
      :row-key="row => row.id"
      @update:checked-row-keys="handleSelectionChange"
      @update:page="handlePageChange"
    />

    <!-- 新增/编辑对话框 -->
    <n-modal
      v-model:show="dialogVisible"
      :title="dialogTitle"
      preset="card"
      :style="{ width: '600px' }"
      :mask-closable="false"
    >
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="采购单号" path="purchaseNo">
              <n-input v-model:value="form.purchaseNo" placeholder="系统自动生成" disabled />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="供应商" path="supplier">
              <n-input v-model:value="form.supplier" placeholder="请输入供应商名称" />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="采购日期" path="purchaseDate">
              <n-date-picker
                v-model:value="form.purchaseDate"
                type="date"
                clearable
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="采购员" path="purchaser">
              <n-input v-model:value="form.purchaser" placeholder="请输入采购员姓名" />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-form-item label="采购商品" path="items">
          <n-dynamic-input
            v-model:value="form.items"
            :on-create="() => ({ productName: '', quantity: 1, price: 0, amount: 0 })"
          >
            <template #default="{ value, index }">
              <div style="display: flex; align-items: center; width: 100%">
                <n-input
                  v-model:value="value.productName"
                  placeholder="商品名称"
                  style="width: 40%"
                  @update:value="updateItemAmount(index)"
                />
                <n-input-number
                  v-model:value="value.quantity"
                  placeholder="数量"
                  :min="1"
                  style="width: 20%; margin: 0 8px"
                  @update:value="updateItemAmount(index)"
                />
                <n-input-number
                  v-model:value="value.price"
                  placeholder="单价"
                  :min="0"
                  :precision="2"
                  style="width: 20%; margin-right: 8px"
                  @update:value="updateItemAmount(index)"
                />
                <n-input-number
                  v-model:value="value.amount"
                  placeholder="金额"
                  :min="0"
                  :precision="2"
                  disabled
                  style="width: 20%"
                />
              </div>
            </template>
          </n-dynamic-input>
        </n-form-item>
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="支付方式" path="paymentMethod">
              <n-select
                v-model:value="form.paymentMethod"
                :options="paymentOptions"
                placeholder="请选择支付方式"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="采购状态" path="status">
              <n-select
                v-model:value="form.status"
                :options="statusOptions"
                placeholder="请选择采购状态"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-form-item label="总金额" path="totalAmount">
          <n-input-number
            v-model:value="form.totalAmount"
            :min="0"
            :precision="2"
            disabled
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="预计到货日期" path="expectedDeliveryDate">
          <n-date-picker
            v-model:value="form.expectedDeliveryDate"
            type="date"
            clearable
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="form.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, h, computed, reactive, watch } from 'vue'
import { useDialog, useMessage } from 'naive-ui'
import { 
  RefreshOutline, AddOutline, TrashOutline, PencilOutline, SearchOutline,
  EyeOutline, CheckmarkCircleOutline, CloseCircleOutline
} from '@vicons/ionicons5'
import { 
  NButton, NSpace, NIcon, NDataTable, NModal, NForm, NFormItem, NInput, 
  NInputNumber, NSelect, NDatePicker, NGrid, NGridItem, NDynamicInput
} from 'naive-ui'

// 模拟数据
const mockData = [
  {
    id: 1,
    purchaseNo: '**********',
    supplier: '供应商A',
    purchaseDate: new Date('2023-01-10').getTime(),
    purchaser: '张三',
    items: [
      { productName: '原材料A', quantity: 100, price: 10.5, amount: 1050 },
      { productName: '原材料B', quantity: 50, price: 20.8, amount: 1040 }
    ],
    paymentMethod: '银行转账',
    status: '已完成',
    totalAmount: 2090,
    expectedDeliveryDate: new Date('2023-01-20').getTime(),
    actualDeliveryDate: new Date('2023-01-19').getTime(),
    remark: '常规采购',
    createTime: '2023-01-10 09:30:00',
    updateTime: '2023-01-19 14:20:00'
  },
  {
    id: 2,
    purchaseNo: 'PO20230002',
    supplier: '供应商B',
    purchaseDate: new Date('2023-02-15').getTime(),
    purchaser: '李四',
    items: [
      { productName: '原材料C', quantity: 200, price: 5.5, amount: 1100 }
    ],
    paymentMethod: '现金',
    status: '待收货',
    totalAmount: 1100,
    expectedDeliveryDate: new Date('2023-02-25').getTime(),
    actualDeliveryDate: null,
    remark: '紧急采购',
    createTime: '2023-02-15 11:20:00',
    updateTime: '2023-02-15 11:20:00'
  },
  {
    id: 3,
    purchaseNo: 'PO20230003',
    supplier: '供应商C',
    purchaseDate: new Date('2023-03-05').getTime(),
    purchaser: '王五',
    items: [
      { productName: '原材料A', quantity: 150, price: 10.2, amount: 1530 },
      { productName: '原材料D', quantity: 80, price: 15.5, amount: 1240 },
      { productName: '原材料E', quantity: 30, price: 25.0, amount: 750 }
    ],
    paymentMethod: '银行转账',
    status: '已取消',
    totalAmount: 3520,
    expectedDeliveryDate: new Date('2023-03-15').getTime(),
    actualDeliveryDate: null,
    remark: '供应商无法按时供货，已取消',
    createTime: '2023-03-05 10:15:00',
    updateTime: '2023-03-08 16:30:00'
  }
]

// 状态变量
const tableRef = ref(null)
const formRef = ref(null)
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('新增采购')
const isEdit = ref(false)
const selectedRows = ref([])
const searchKeyword = ref('')
const dateRange = ref(null)
const purchaseData = ref([...mockData])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  }
})

// 表单数据
const form = ref({
  id: null,
  purchaseNo: '',
  supplier: '',
  purchaseDate: null,
  purchaser: '',
  items: [],
  paymentMethod: null,
  status: '待审核',
  totalAmount: 0,
  expectedDeliveryDate: null,
  actualDeliveryDate: null,
  remark: ''
})

// 表单验证规则
const rules = {
  supplier: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' }
  ],
  purchaseDate: [
    { required: true, type: 'number', message: '请选择采购日期', trigger: 'change' }
  ],
  purchaser: [
    { required: true, message: '请输入采购员姓名', trigger: 'blur' }
  ],
  items: [
    { 
      required: true, 
      type: 'array', 
      min: 1, 
      message: '请至少添加一个采购商品', 
      trigger: 'change' 
    }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择采购状态', trigger: 'change' }
  ],
  expectedDeliveryDate: [
    { required: true, type: 'number', message: '请选择预计到货日期', trigger: 'change' }
  ]
}

// 支付方式选项
const paymentOptions = [
  { label: '银行转账', value: '银行转账' },
  { label: '现金', value: '现金' },
  { label: '支票', value: '支票' },
  { label: '信用证', value: '信用证' },
  { label: '其他', value: '其他' }
]

// 采购状态选项
const statusOptions = [
  { label: '待审核', value: '待审核' },
  { label: '已审核', value: '已审核' },
  { label: '待收货', value: '待收货' },
  { label: '部分收货', value: '部分收货' },
  { label: '已完成', value: '已完成' },
  { label: '已取消', value: '已取消' }
]

// 工具实例
const dialog = useDialog()
const message = useMessage()

// 表格列配置
const columns = [
  { type: 'selection', width: 50 },
  { title: 'ID', key: 'id', width: 60 },
  { title: '采购单号', key: 'purchaseNo', width: 120 },
  { title: '供应商', key: 'supplier', width: 120 },
  { 
    title: '采购日期', 
    key: 'purchaseDate', 
    width: 120,
    sorter: (a, b) => a.purchaseDate - b.purchaseDate,
    render(row) {
      return h('span', null, new Date(row.purchaseDate).toLocaleDateString())
    }
  },
  { title: '采购员', key: 'purchaser', width: 100 },
  { 
    title: '商品数量', 
    key: 'itemCount', 
    width: 100,
    render(row) {
      return h('span', null, row.items.length)
    }
  },
  { 
    title: '总金额', 
    key: 'totalAmount', 
    width: 120,
    sorter: (a, b) => a.totalAmount - b.totalAmount,
    render(row) {
      return h('span', { style: { fontWeight: 'bold' } }, `¥${row.totalAmount.toFixed(2)}`)
    }
  },
  { 
    title: '支付方式', 
    key: 'paymentMethod', 
    width: 100
  },
  { 
    title: '采购状态', 
    key: 'status', 
    width: 100,
    render(row) {
      const statusColorMap = {
        '待审核': '#f0a020',
        '已审核': '#2080f0',
        '待收货': '#2080f0',
        '部分收货': '#18a058',
        '已完成': '#18a058',
        '已取消': '#d03050'
      }
      return h('span', { style: { color: statusColorMap[row.status] || '#333' } }, row.status)
    }
  },
  { 
    title: '预计到货日期', 
    key: 'expectedDeliveryDate', 
    width: 120,
    render(row) {
      return h('span', null, row.expectedDeliveryDate ? new Date(row.expectedDeliveryDate).toLocaleDateString() : '-')
    }
  },
  { 
    title: '实际到货日期', 
    key: 'actualDeliveryDate', 
    width: 120,
    render(row) {
      return h('span', null, row.actualDeliveryDate ? new Date(row.actualDeliveryDate).toLocaleDateString() : '-')
    }
  },
  { title: '备注', key: 'remark', ellipsis: true },
  {
    title: '操作',
    key: 'actions',
    width: 240,
    fixed: 'right',
    render(row) {
      return h(NSpace, { justify: 'center', size: 'small' }, [
        h(NButton, {
          size: 'small',
          type: 'info',
          onClick: () => viewPurchaseDetails(row)
        }, {
          default: () => '查看',
          icon: () => h(NIcon, null, { default: () => h(EyeOutline) })
        }),
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleEdit(row)
        }, {
          default: () => '编辑',
          icon: () => h(NIcon, null, { default: () => h(PencilOutline) })
        }),
        row.status !== '已完成' && row.status !== '已取消' ? h(NButton, {
          size: 'small',
          type: 'success',
          onClick: () => confirmPurchase(row)
        }, {
          default: () => '确认收货',
          icon: () => h(NIcon, null, { default: () => h(CheckmarkCircleOutline) })
        }) : null,
        row.status !== '已完成' && row.status !== '已取消' ? h(NButton, {
          size: 'small',
          type: 'error',
          onClick: () => cancelPurchase(row)
        }, {
          default: () => '取消',
          icon: () => h(NIcon, null, { default: () => h(CloseCircleOutline) })
        }) : null
      ].filter(Boolean))
    }
  }
]

// 根据搜索条件过滤数据
const filteredData = computed(() => {
  let result = [...purchaseData.value]
  
  // 按关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(item => 
      item.purchaseNo.toLowerCase().includes(keyword) ||
      item.supplier.toLowerCase().includes(keyword) ||
      item.purchaser.toLowerCase().includes(keyword) ||
      item.paymentMethod.toLowerCase().includes(keyword) ||
      item.status.toLowerCase().includes(keyword) ||
      item.remark?.toLowerCase().includes(keyword)
    )
  }
  
  // 按日期范围过滤
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    const startDate = new Date(dateRange.value[0]).setHours(0, 0, 0, 0)
    const endDate = new Date(dateRange.value[1]).setHours(23, 59, 59, 999)
    result = result.filter(item => {
      const purchaseDate = new Date(item.purchaseDate).getTime()
      return purchaseDate >= startDate && purchaseDate <= endDate
    })
  }
  
  return result
})

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})

// 获取采购数据
const fetchData = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    purchaseData.value = [...mockData]
    loading.value = false
  }, 500)
}

// 刷新数据
const refreshData = () => {
  fetchData()
  message.success('数据已刷新')
}

// 显示新增对话框
const showAddDialog = () => {
  dialogTitle.value = '新增采购'
  isEdit.value = false
  form.value = {
    id: null,
    purchaseNo: generatePurchaseNo(),
    supplier: '',
    purchaseDate: Date.now(),
    purchaser: '',
    items: [{ productName: '', quantity: 1, price: 0, amount: 0 }],
    paymentMethod: null,
    status: '待审核',
    totalAmount: 0,
    expectedDeliveryDate: null,
    actualDeliveryDate: null,
    remark: ''
  }
  dialogVisible.value = true
}

// 生成采购单号
const generatePurchaseNo = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `PO${year}${month}${day}${random}`
}

// 查看采购详情
const viewPurchaseDetails = (row) => {
  // 这里可以实现查看详情的逻辑，例如打开一个只读的详情对话框
  message.info(`查看采购单 ${row.purchaseNo} 的详情`)
}

// 确认收货
const confirmPurchase = (row) => {
  dialog.info({
    title: '确认收货',
    content: `确定要将采购单 "${row.purchaseNo}" 标记为已完成吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      // 模拟确认收货操作
      const index = purchaseData.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        purchaseData.value[index] = {
          ...purchaseData.value[index],
          status: '已完成',
          actualDeliveryDate: Date.now(),
          updateTime: new Date().toLocaleString()
        }
        message.success('确认收货成功')
      }
    }
  })
}

// 取消采购
const cancelPurchase = (row) => {
  dialog.warning({
    title: '取消采购',
    content: `确定要取消采购单 "${row.purchaseNo}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      // 模拟取消采购操作
      const index = purchaseData.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        purchaseData.value[index] = {
          ...purchaseData.value[index],
          status: '已取消',
          updateTime: new Date().toLocaleString()
        }
        message.success('取消采购成功')
      }
    }
  })
}

// 处理编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑采购'
  isEdit.value = true
  // 深拷贝数据，避免直接修改原数据
  form.value = JSON.parse(JSON.stringify(row))
  dialogVisible.value = true
}

// 处理删除
const handleDelete = (row) => {
  dialog.warning({
    title: '警告',
    content: `确定要删除采购单 "${row.purchaseNo}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      // 模拟删除操作
      purchaseData.value = purchaseData.value.filter(item => item.id !== row.id)
      message.success('删除成功')
    }
  })
}

// 批量删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要删除的采购单')
    return
  }
  
  dialog.warning({
    title: '警告',
    content: `确定要删除选中的 ${selectedRows.value.length} 个采购单吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      // 模拟批量删除操作
      const ids = selectedRows.value.map(row => row.id)
      purchaseData.value = purchaseData.value.filter(item => !ids.includes(item.id))
      selectedRows.value = []
      message.success('批量删除成功')
    }
  })
}

// 更新商品金额
const updateItemAmount = (index) => {
  if (!form.value.items[index]) return
  
  const item = form.value.items[index]
  item.amount = (item.quantity || 0) * (item.price || 0)
  
  // 更新总金额
  calculateTotalAmount()
}

// 计算总金额
const calculateTotalAmount = () => {
  form.value.totalAmount = form.value.items.reduce((sum, item) => {
    return sum + (item.amount || 0)
  }, 0)
}

// 监听产品变化，自动计算总金额
watch(() => form.value.items, () => {
  calculateTotalAmount()
}, { deep: true })

// 处理保存
const handleSave = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return
    
    // 模拟保存操作
    if (isEdit.value) {
      // 更新
      const index = purchaseData.value.findIndex(item => item.id === form.value.id)
      if (index !== -1) {
        purchaseData.value[index] = {
          ...form.value,
          updateTime: new Date().toLocaleString()
        }
        message.success('更新成功')
      }
    } else {
      // 新增
      const newId = Math.max(...purchaseData.value.map(item => item.id), 0) + 1
      const now = new Date().toLocaleString()
      purchaseData.value.push({
        ...form.value,
        id: newId,
        createTime: now,
        updateTime: now
      })
      message.success('添加成功')
    }
    
    dialogVisible.value = false
  })
}

// 处理选择变化
const handleSelectionChange = (keys) => {
  selectedRows.value = purchaseData.value.filter(item => keys.includes(item.id))
}

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
}
</script>

<style scoped>
.purchase-page {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  margin-bottom: 16px;
}

.n-data-table {
  flex: 1;
}
</style>
