import eventBus from '@/utils/eventBus'
import router from '@/router'
import { useMainStore } from '@/stores/mainStore'

// 初始化认证事件处理
export function initAuthEventHandlers() {
  // 监听登出事件
  eventBus.on('auth:logout', async () => {
    console.log('收到登出事件，准备跳转到登录页')

    // 获取 mainStore 实例
    const mainStore = useMainStore()

    // 调用 logout 方法清理状态并等待完成
    await mainStore.logout()
    console.log('登出完成，准备跳转到登录页')

    // 确保在 logout 完成后再跳转
    router.push('/login')
  })
}

export default {
  initAuthEventHandlers
}
