<template>
  <div class="deposit-order-page">
    <!-- 筛选条件区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">定金日期</div>
          <div class="filter-options">
            <n-radio-group v-model:value="filterForm.dateRange" @update:value="handleDateRangeChange" class="custom-radio-group">
              <n-radio-button v-for="option in dateRangeOptions" :key="option.value" :value="option.value" class="custom-radio-button">
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">意向车型</div>
          <div class="filter-options">
            <n-radio-group v-model:value="filterForm.vehicleCategory" @update:value="handleSearch" class="custom-radio-group">
              <n-radio-button v-for="option in vehicleCategoryOptions" :key="option.value" :value="option.value" class="custom-radio-button">
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">定金状态</div>
          <div class="filter-options">
            <n-radio-group v-model:value="filterForm.depositStatus" @update:value="handleSearch" class="custom-radio-group">
              <n-radio-button v-for="option in depositStatusOptions" :key="option.value" :value="option.value" class="custom-radio-button">
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">销售单位</div>
          <div class="filter-options org-selector-container">
            <department-selector
              v-model="filterForm.invoiceOrg"
              mode="single"
              label="请选择销售单位"
              @update:model-value="handleSearch"
            />
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><component :is="RefreshOutlineIcon" /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><component :is="AddOutlineIcon" /></n-icon>
          </template>
          定金订单
        </n-button>

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="输入关键字进行搜索"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><component :is="SearchOutlineIcon" /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="filteredData"
      :loading="loading"
      :pagination="pagination"
      :row-key="row => row.id"
      :scroll-x="1800"
      @update:checked-row-keys="handleSelectionChange"
      @update:page="handlePageChange"
    />

    <!-- 定金订单弹窗 -->
    <deposit-order-modal
      ref="depositOrderModalRef"
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :title="dialogTitle"
      @save="handleSaveSuccess"
      @cancel="dialogVisible = false"
    />

    <!-- 详情弹窗 -->
    <order-detail-modal
      v-model:visible="detailDialogVisible"
      :id="currentDetailId"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h, markRaw } from 'vue'
import messages from '@/utils/messages'
import vehicleOrderApi from '@/api/vehicleOrder'
import { NIcon, NTag } from 'naive-ui'
import { getDictOptions } from '@/api/dict'
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  EyeOutline,
  CreateOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  ArchiveOutline,
  CarOutline,
  CopyOutline
} from '@vicons/ionicons5'
import { dateRangeOptions, getDateRangeParams, handleDateRangeChange as handleDateChange, handleCustomDateChange as handleCustomDate } from '@/utils/dateRange'

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const SearchOutlineIcon = markRaw(SearchOutline)
const RefreshOutlineIcon = markRaw(RefreshOutline)
const AddOutlineIcon = markRaw(AddOutline)
const EyeOutlineIcon = markRaw(EyeOutline)
const CreateOutlineIcon = markRaw(CreateOutline)
const CarOutlineIcon = markRaw(CarOutline)
const CopyOutlineIcon = markRaw(CopyOutline)

import OrderDetailModal from '@/components/orders/OrderDetailModal.vue'
import DepositOrderModal from '@/components/orders/DepositOrderModal.vue'
import DepartmentSelector from '@/components/users/DepartmentSelector.vue'

// 状态变量
const tableRef = ref(null)
const depositOrderModalRef = ref(null)
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('定金订单')
const isEdit = ref(false)
const selectedRows = ref([])

// 日期范围选项已从utils/dateRange.js导入

// 车辆类别选项
const vehicleCategoryOptions = ref([
  { label: '不限', value: null }
])

// 定金状态选项
const depositStatusOptions = ref([
  { label: '不限', value: null }
])

// 筛选表单
const filterForm = reactive({
  dateRange: null,
  customDateRange: null,
  vehicleCategory: null,
  invoiceOrg: null,
  minAmount: null,
  maxAmount: null,
  keywords: '',
  depositStatus: null
})

// 数据列表
const ordersData = ref([])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0,
  onChange: (page) => {
    pagination.page = page
    refreshData()
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }
})

// 表格列配置
const columns = [
  { type: 'selection', width: 50, fixed: 'left' },

  {
    title: '订单编号',
    key: 'orderSn',
    width: 230,
    fixed: 'left',
    render(row) {
      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer'
        },
        onClick: () => copyToClipboard(row.orderSn),
        title: '点击复制订单编号'
      }, [
        h(NIcon, {
          size: 16,
          color: 'var(--primary-color)',
          style: {
            opacity: 0.8,
            transition: 'opacity 0.2s'
          },
          onMouseover: (e) => {
            e.target.style.opacity = 1
          },
          onMouseout: (e) => {
            e.target.style.opacity = 0.8
          }
        }, { default: () => h(CopyOutlineIcon) }),
        h('span', {
          style: {
            marginLeft: '5px',
            fontFamily: 'CustomChinese',
            letterSpacing: '0.5px',
            fontWeight: 600
          }
        }, row.orderSn),

      ])
    }
  },
  {
    title: '定金日期',
    key: 'depositDate',
    width: 120,
    render(row) {
      if (!row.depositDate) return '未设置';
      const date = new Date(row.depositDate);
      return date.toLocaleDateString('zh-CN');
    }
  },
  {
    title: '定金状态',
    key: 'depositStatus',
    width: 100,
    render(row) {
      // 定义状态类型映射
      const statusTypeMap = {
        'received': { type: 'success', color: '#18a058' },
        'refunded': { type: 'error', color: '#d03050' },
        'transferred': { type: 'info', color: '#2080f0' },
        'expired': { type: 'warning', color: '#f0a020' }
      }

      // 查找状态对应的选项
      const statusOption = depositStatusOptions.value.find(option =>
        option.value === row.depositStatus
      )

      // 获取状态文本和类型
      const statusText = statusOption ? statusOption.label : (row.depositStatus || '未知')
      const statusType = statusTypeMap[row.depositStatus] || { type: 'default', color: '#909399' }

      return h(
        NTag,
        {
          type: statusType.type,
          bordered: false,
          style: {
            padding: '2px 8px',
            fontWeight: 'bold'
          }
        },
        { default: () => statusText }
      )
    }
  },
  {
    title: '销售单位',
    key: 'salesOrgName',
    width: 200
  },
  {
    title: '销售顾问',
    key: 'salesAgentName',
    width: 100
  },
  {
    title: '客户名称',
    key: 'customerName',
    width: 120
  },
  {
    title: '联系电话',
    key: 'mobile',
    width: 120,
    render(row) {
      // 手机号脱敏处理：隐藏中间四位，以****代替
      if (!row.mobile) return '';
      if (row.mobile.length === 11) {
        return row.mobile.substring(0, 3) + '****' + row.mobile.substring(7);
      }
      return row.mobile;
    }
  },
  {
    title: '意向车型',
    key: 'intentionModel',
    width: 200
  },
  {
    title: '定金金额(元)',
    key: 'depositAmount',
    width: 140,
    sorter: (a, b) => a.depositAmount - b.depositAmount,
    render(row) {
      // depositAmount单位是分，需要转换为元
      const amountInYuan = row.depositAmount / 100;
      // 使用toLocaleString格式化为千分位，保留2位小数
      const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
      return h('span', { style: { fontWeight: 'bold' } }, `¥${formattedAmount}`)
    }
  },
  {
    title: '预计交付日期',
    key: 'expectedDeliveryDate',
    width: 120,
    render(row) {
      if (!row.expectedDeliveryDate) return '未设置';
      const date = new Date(row.expectedDeliveryDate);
      return date.toLocaleDateString('zh-CN');
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 160,
    fixed: 'right',
    align: 'center',
    render: (row) => {
      const actions = [];

      // 查看按钮 - 所有状态都可以查看
      actions.push(
        h(
          'div',
          {
            style: {
              cursor: 'pointer',
              color: '#2080f0',
              fontSize: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            },
            onClick: () => handleView(row.id),
            title: '查看详情'
          },
          [h(NIcon, { size: 20 }, { default: () => h(EyeOutlineIcon) })]
        )
      );

      // 编辑按钮 - 只有已收定金状态可以编辑
      if (row.depositStatus === 'received') {
        actions.push(
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#18a058',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              onClick: () => handleEdit(row.id),
              title: '编辑定金订单'
            },
            [h(NIcon, { size: 20 }, { default: () => h(CreateOutlineIcon) })]
          )
        );
      }

      // 转为销售订单按钮 - 只有已收定金状态可以转为销售订单
      if (row.depositStatus === 'received') {
        actions.push(
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#2080f0',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              onClick: () => handleConvertToOrder(row.id),
              title: '转为销售订单'
            },
            [h(NIcon, { size: 20 }, { default: () => h(CarOutlineIcon) })]
          )
        );
      }

      return h('div', {
        style: {
          display: 'flex',
          justifyContent: 'center',
          gap: '8px'
        }
      }, actions)
    }
  }
]

// 表格数据
const filteredData = computed(() => {
  return ordersData.value
})

// 获取车辆品牌选项
const fetchVehicleBrandOptions = async () => {
  try {
    const res = await getDictOptions('vehicle_brand')
    if (res && res.code === 200 && res.data) {
      // 转换字典数据为选择器选项格式
      const options = res.data.map(item => ({
        label: item.option_label,
        value: item.option_value
      }))

      // 添加"不限"选项
      vehicleCategoryOptions.value = [
        { label: '不限', value: null },
        ...options
      ]
    }
  } catch (error) {
    console.error('获取车辆品牌选项失败:', error)
    messages.error('获取车辆品牌选项失败')
  }
}

// 获取定金状态选项
const fetchDepositStatusOptions = async () => {
  try {
    const res = await getDictOptions('deposit_status')
    if (res && res.code === 200 && res.data) {
      // 转换字典数据为选择器选项格式
      const options = res.data.map(item => ({
        label: item.option_label,
        value: item.option_value
      }))

      // 添加"不限"选项
      depositStatusOptions.value = [
        { label: '不限', value: null },
        ...options
      ]
    }
  } catch (error) {
    console.error('获取定金状态选项失败:', error)
    messages.error('获取定金状态选项失败')
  }
}

// 初始化
onMounted(() => {
  // 获取字典数据
  fetchVehicleBrandOptions()
  fetchDepositStatusOptions()

  // 刷新表格数据
  refreshData()
})

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.page,
      size: pagination.pageSize
    }

    // 添加筛选条件
    if (filterForm.keywords) {
      params.keywords = filterForm.keywords
    }

    // 处理日期范围
    if (filterForm.dateRange) {
      const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
      if (dateRange.startDate) params.startDate = dateRange.startDate
      if (dateRange.endDate) params.endDate = dateRange.endDate
    }

    // 处理车辆类别/意向车型
    if (filterForm.vehicleCategory) {
      params.intentionModel = filterForm.vehicleCategory
    }

    // 处理销售单位
    if (filterForm.invoiceOrg) {
      params.salesOrgId = filterForm.invoiceOrg.id
    }

    // 处理定金状态
    if (filterForm.depositStatus) {
      params.depositStatus = filterForm.depositStatus
    }

    // 调用API获取数据
    const response = await vehicleOrderApi.getDepositOrderList(params)

    if (response.code === 200) {
      // 直接使用返回的数据列表
      ordersData.value = response.data.list

      // 更新分页信息
      pagination.itemCount = response.data.total
      pagination.pageCount = response.data.pages
    } else {
      messages.error(response.message || '数据加载失败')
    }
  } catch (error) {
    messages.error('加载数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理日期范围变化
const handleDateRangeChange = (value) => {
  handleDateChange(value, filterForm, handleSearch)
}

// 处理自定义日期变化
const handleCustomDateChange = (dates) => {
  handleCustomDate(dates, handleSearch)
}

// 处理查询
const handleSearch = () => {
  pagination.page = 1
  refreshData()
}

// 显示新增对话框
const showAddDialog = () => {
  // 重置表单
  if (depositOrderModalRef.value) {
    depositOrderModalRef.value.resetForm()
  }

  // 打开定金订单弹窗
  isEdit.value = false
  dialogTitle.value = '新增定金订单'
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (id) => {
  try {
    loading.value = true

    // 调用API获取详细数据
    const response = await vehicleOrderApi.getDepositOrderDetail(id)

    if (response.code === 200) {
      const apiData = response.data

      isEdit.value = true
      dialogTitle.value = '编辑定金订单'

      // 设置表单数据
      if (depositOrderModalRef.value) {
        depositOrderModalRef.value.setFormData(apiData)
      }

      dialogVisible.value = true
    } else {
      messages.error(response.message || '获取定金订单数据失败')
    }
  } catch (error) {
    console.error('获取定金订单数据失败:', error)
    messages.error('获取定金订单数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 详情弹窗
const detailDialogVisible = ref(false)
const currentDetailId = ref(null)

// 处理查看
const handleView = (id) => {
  currentDetailId.value = id
  detailDialogVisible.value = true
}

// 处理保存成功
const handleSaveSuccess = () => {
  refreshData() // 刷新数据列表
}

// 处理选择变化
const handleSelectionChange = (keys) => {
  selectedRows.value = ordersData.value.filter(item => keys.includes(item.id))
}

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page
  // 页码变化由分页组件的 onChange 事件处理
}

// 复制文本到剪贴板
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text)
    .then(() => {
      messages.success('已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      messages.error('复制失败')
    })
}

// 处理转为销售订单
const handleConvertToOrder = (id) => {
  // 这里可以实现转为销售订单的逻辑
  messages.info(`将定金订单 ${id} 转为销售订单的功能待实现`)
}
</script>

<style scoped>
.deposit-order-page {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-card {
  margin-bottom: 0;
}

.toolbar {
  margin-bottom: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-separator {
  color: #999;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.department-selector) {
  width: 350px !important;
  min-width: 350px !important;
}

/* 自定义单选按钮组样式 */
:deep(.custom-radio-group .custom-radio-button) {
  border: none;
  background: transparent;
  transition: all 0.2s;
}

:deep(.custom-radio-group .custom-radio-button:hover) {
  color: var(--primary-color);
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.custom-radio-group .custom-radio-button--checked) {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border: none !important;
}

.n-data-table {
  flex: 1;
  overflow: auto;
}

/* 表格滚动样式 */
:deep(.n-data-table-base-table-body) {
  overflow-x: auto !important;
}

/* 冻结列样式 */
:deep(.n-data-table-thead .n-data-table-th.n-data-table-th--fixed-left),
:deep(.n-data-table-tbody .n-data-table-td.n-data-table-td--fixed-left) {
  background-color: var(--n-merged-th-color);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}
</style>
