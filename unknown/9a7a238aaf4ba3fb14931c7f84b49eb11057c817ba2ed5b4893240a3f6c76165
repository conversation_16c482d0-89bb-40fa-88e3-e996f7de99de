<script setup>
import { ref, onMounted, h } from 'vue'
import { NSpace, NCard, NGrid, NGridItem, NDataTable, NButton, NModal, NForm, NFormItem, NInput, NTreeSelect, NSelect, NSwitch, NIcon, NTree, NDropdown } from 'naive-ui'
import { getDepartments, getDepartmentMembers, getRoles, getUserDetails, createUser, updateUser } from '@/api/users'
import { AddCircleOutline,PencilOutline,TrashOutline} from '@vicons/ionicons5'
import { Edit, TrashCan } from '@vicons/carbon'
import {PlusSquareOutlined,MinusSquareOutlined} from '@vicons/antd'
import {MoreVertical16Filled} from '@vicons/fluent'

const departmentTree = ref([])
const selectedDepartmentKeys = ref([])
const users = ref([])
const pagination = ref({
  page: 1,
  pageSize: 50,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  placement: 'bottom-right'
})
const selectedRowKeys = ref([])
const showUserModal = ref(false)
const editingUser = ref({})
const roleOptions = ref([])
const userForm = ref(null)
const expandedKeys = ref([])

// 新增的状态变量
const showDepartmentModal = ref(false)
const editingDepartment = ref({})
const departmentForm = ref(null)

// 新增的部门表单规则
const departmentRules = {
  name: { required: true, message: '请输入部门名称', trigger: 'blur' },
}

const userRules = {
  username: { required: true, message: '请输入用户名', trigger: 'blur' },
  nickname: { required: true, message: '请输入姓名', trigger: 'blur' },
  passwd: { required: true, message: '请输入密码', trigger: 'blur' },
  workMobile: { required: true, message: '请输入手机号', trigger: 'blur' },
  departmentId: { required: true, message: '请选择所属部门', trigger: 'change' },
  roles: { type: 'array', required: true, message: '请选择角色', trigger: 'change' }
}

const columns = [
  { title: 'ID', key: 'id', width: 80, align: "center" },  // 添加回 ID 列，内容居中
  { title: '姓名', key: 'nickname', align: "center" },  // 内容居中
  { title: '职务', key: 'position', align: "center" },  // 内容居中
  { title: '手机号码', key: 'workMobile', align: "center" },  // 内容居中
  {
    title: '角色',
    key: 'roles',
    align: "center",  // 内容居中
    render: (row) => {
      if (!roleOptions.value || roleOptions.value.length === 0) return ''
      return row.roles
        .map(roleId => roleOptions.value.find(role => role.value === roleId)?.label)
        .filter(Boolean)  // 过滤掉可能的 undefined 值
        .join(', ')
    }
  },
  {
    title: '操作',
    key: 'actions',
    align: "center",  // 操作列居中（已有设置）
    render: (row) => {
      return h(NSpace, { align: 'center' }, {
        default: () => [
          h(NSwitch, {
            value: !row.disabled,
            onUpdateValue: (value) => toggleUserStatus(row, value)
          }),
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => editUser(row)
          }, { default: () => h(NIcon, { component: Edit }) }),
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => deleteUser(row)
          }, { default: () => h(NIcon, { component: TrashCan }) })
        ]
      })
    }
  }
]

onMounted(async () => {
  await fetchRoles()
  await fetchDepartments()
  if (departmentTree.value.length > 0) {
    selectedDepartmentKeys.value = [departmentTree.value[0].key]
    await fetchUsers(selectedDepartmentKeys.value[0])
  }
})

function buildTreeData(departments) {
  const options = []
  const map = {}

  // 第一步：创建所有节点的映射
  departments.forEach(dept => {
    map[dept.id] = {
      key: dept.id.toString(),
      label: dept.name,
      children: [],
      isLeaf: true, // 初始假设所有节点都是叶子节点
      rawData: dept // 保存原始数据
    }
  })

  // 第二步：构建树形结构
  departments.forEach(dept => {
    if (dept.parentId === 0 || !dept.parentId) {
      // 根节点直接添加到结果中
      options.push(map[dept.id])
    } else {
      // 子节点添加到其父节点下
      const parent = map[dept.parentId]
      if (parent) {
        parent.children.push(map[dept.id])
        parent.isLeaf = false // 如果有子节点，则不是叶子节点
      } else {
        // 如果找不到父节点，将其作为根节点处理
        console.warn(`Parent node with ID ${dept.parentId} not found for department ${dept.name} (ID: ${dept.id}). Treating as root node.`)
        options.push(map[dept.id])
      }
    }
  })

  return options
}

async function fetchDepartments() {
  try {
    const response = await getDepartments()
    departmentTree.value = buildTreeData(response.data)
    if (departmentTree.value.length > 0) {
      selectedDepartmentKeys.value = [departmentTree.value[0].key]
      // 初始化 expandedKeys 为所有顶级部门的 key
      expandedKeys.value = departmentTree.value.map(dept => dept.key)
      await fetchUsers(selectedDepartmentKeys.value[0])
    }
  } catch (error) {
    console.error('Failed to fetch departments:', error)
  }
}

async function fetchUsers(departmentId) {
  try {
    const response = await getDepartmentMembers(departmentId, pagination.value.page, pagination.value.pageSize)
    users.value = response.data.map(user => ({
      ...user,
      roles: user.roles || []  // 确保每个用户都有 roles 属性，即使它可能为空
    }))
    pagination.value.itemCount = response.total
  } catch (error) {
    console.error('Failed to fetch users:', error)
    users.value = []
  }
}

async function fetchRoles() {
  try {
    const response = await getRoles()
    roleOptions.value = response.data.map(role => ({
      label: role.roleName,
      value: role.id
    }))
  } catch (error) {
    console.error('Failed to fetch roles:', error)
    roleOptions.value = []  // 如果获取失败，设置为空数组
  }
}

function handlePageChange(page) {
  pagination.value.page = page
  fetchUsers(selectedDepartmentKeys.value[0])
}

function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  fetchUsers(selectedDepartmentKeys.value[0])
}

function handleCheck(rowKeys) {
  selectedRowKeys.value = rowKeys
}

function handleAddUser() {
  editingUser.value = {
    username: '',
    nickname: '',
    passwd: '',
    workMobile: '',
    departmentId: null,
    roles: [],
    disabled: false
  }
  showUserModal.value = true
}

async function editUser(user) {
  try {
    const response = await getUserDetails(user.id)
    editingUser.value = response.data
    showUserModal.value = true
  } catch (error) {
    console.error('Failed to get user details:', error)
  }
}

function closeUserModal() {
  showUserModal.value = false
  editingUser.value = {}
}

async function saveUser() {
  try {
    await userForm.value.validate()
    if (editingUser.value.id) {
      await updateUser(editingUser.value)
    } else {
      await createUser(editingUser.value)
    }
    closeUserModal()
    await fetchUsers(selectedDepartmentKeys.value[0])
  } catch (error) {
    console.error('Failed to save user:', error)
  }
}

async function toggleUserStatus(user, value) {
  try {
    await updateUser({ id: user.id, disabled: !value })
    await fetchUsers(selectedDepartmentKeys.value[0])
  } catch (error) {
    console.error('Failed to toggle user status:', error)
  }
}

async function deleteUser(user) {
  // Implement delete user logic
}

async function handleBatchDelete() {
  // Implement batch delete logic
}

function renderSwitcherIcon({ expanded, isLeaf }) {
  if (isLeaf) {
    return null
  }
  return expanded ? h(MinusSquareOutlined) : h(PlusSquareOutlined)
}

// 修改 overrideNodeClickBehavior 函数
const overrideNodeClickBehavior = ({ option }, e) => {
  // 阻止事件冒泡，以防止触发两次
  if (e) e.stopPropagation()

  // 更新选中状态
  selectedDepartmentKeys.value = [option.key]

  // 加载用户列表
  fetchUsers(option.key)

  // 处理展开/收起
  if (option.children && option.children.length > 0) {
    // 如果当前节点已展开，则收起
    if (expandedKeys.value.includes(option.key)) {
      expandedKeys.value = expandedKeys.value.filter(key => key !== option.key)
    } else {
      // 如果当前节点未展开，则展开当前节点，同时收起其他所有节点
      // 保留所有父节点的展开状态
      const parentKeys = findAllParentKeys(departmentTree.value, option.key)
      expandedKeys.value = [...parentKeys, option.key]
    }
  } else {
    // 如果点击的是叶子节点，收起所有其他节点，只保留当前节点的父节点展开
    const parentKeys = findAllParentKeys(departmentTree.value, option.key)
    expandedKeys.value = [...parentKeys]
  }

  // 返回 'prevent-default' 来阻止默认行为
  return 'prevent-default'
}

function handleTreeExpand(keys) {
  expandedKeys.value = keys
  // 添加一个小延时，确保DOM更新后再触发滚动条更新
  setTimeout(() => {
    // 强制更新滚动容器
    const scrollContainer = document.querySelector('.tree-container')
    if (scrollContainer) {
      scrollContainer.style.overflow = 'hidden'
      setTimeout(() => {
        scrollContainer.style.overflow = 'auto'
      }, 10)
    }
  }, 100)
}

// 添加一个辅助函数来查找节点
function findNodeByKey(tree, key) {
  for (const node of tree) {
    if (node.key === key) {
      return node
    }
    if (node.children) {
      const found = findNodeByKey(node.children, key)
      if (found) return found
    }
  }
  return null
}

// 查找节点的所有父节点的key
function findAllParentKeys(tree, key, parentPath = []) {
  for (const node of tree) {
    // 创建当前路径
    const currentPath = [...parentPath]

    // 如果找到了目标节点，返回父节点路径
    if (node.key === key) {
      return currentPath
    }

    // 如果当前节点有子节点，递归查找
    if (node.children && node.children.length > 0) {
      // 将当前节点添加到路径中
      currentPath.push(node.key)
      const result = findAllParentKeys(node.children, key, currentPath)
      if (result) {
        return result
      }
    }
  }

  // 如果没有找到，返回null
  return null
}

// 新增、编辑和删除部门的函数
async function addDepartment(parentKey) {
  editingDepartment.value = { parentId: parentKey, name: '' }
  showDepartmentModal.value = true
}

async function renameDepartment(key) {
  const department = findNodeByKey(departmentTree.value, key)
  if (department) {
    editingDepartment.value = { ...department }
    showDepartmentModal.value = true
  }
}

async function deleteDepartment(key) {
  // 这里应该调用后端 API 来删除部门
  console.log('删除部门:', key) // 使用key参数，避免未使用警告
  // 删除成功后重新获取部门树
  await fetchDepartments()
}

async function saveDepartment() {
  try {
    await departmentForm.value.validate()
    // 这里应该调用后端 API 来保存或更新部门
    // 保存成功后重新获取部门树
    await fetchDepartments()
    showDepartmentModal.value = false
  } catch (error) {
    console.error('Failed to save department:', error)
  }
}

// 处理拖动排序
function handleDrop(/* { node, dragNode, dropPosition } */) {
  // 这里应该调用后端 API 来更新部门顺序
  // 更新成功后重新获取部门树
  fetchDepartments()
}

// 修改 renderSuffix 函数
function renderSuffix({ option }) {
  return () => h(
    'div',
    { class: 'tree-node-action' },
    h(
      NDropdown,
      {
        trigger: 'click',
        options: [
          {
            label: '新增子部门',
            key: 'add',
            icon: renderIcon(AddCircleOutline)
          },
          {
            label: '重命名',
            key: 'rename',
            icon: renderIcon(PencilOutline)
          },
          {
            label: '删除',
            key: 'delete',
            icon: renderIcon(TrashOutline)
          }
        ],
        onSelect: (key) => {
          switch (key) {
            case 'add':
              addDepartment(option.key)
              break
            case 'rename':
              renameDepartment(option.key)
              break
            case 'delete':
              deleteDepartment(option.key)
              break
          }
        }
      },
      {
        default: () => h(NIcon, { component: MoreVertical16Filled })
      }
    )
  )
}

function renderIcon(icon) {
  return () => h(NIcon, null, { default: () => h(icon) })
}

</script>

<template>
  <n-space vertical :size="24" class="users-page-container">
    <n-card title="" class="main-card">
      <n-grid :cols="12" :x-gap="24" class="main-grid">
        <!-- 左侧组织机构树 -->
        <n-grid-item span="4" class="grid-item">
          <n-card title="组织机构" class="department-card">
            <div class="scrollable-container tree-container">
              <n-tree
                block-line
                :data="departmentTree"
                :expanded-keys="expandedKeys"
                :selected-keys="selectedDepartmentKeys"
                :render-suffix="renderSuffix"
                :render-switcher-icon="renderSwitcherIcon"
                :override-default-node-click-behavior="overrideNodeClickBehavior"
                selectable
                draggable
                :style="{ maxHeight: 'none', height: 'auto' }"
                @update:expanded-keys="handleTreeExpand"
                @drop="handleDrop"
                class="department-tree"
              />
            </div>
          </n-card>
        </n-grid-item>

        <!-- 右侧用户列表 -->
        <n-grid-item span="8" class="grid-item">
          <n-card title="用户列表" class="users-card">
            <template #header-extra>
              <n-space>
                <n-button @click="handleAddUser">新增用户</n-button>
                <n-button @click="handleBatchDelete" :disabled="!selectedRowKeys.length">批量删除</n-button>
              </n-space>
            </template>
            <div class="scrollable-container table-container">
              <n-data-table
                :columns="columns"
                :data="users"
                :pagination="pagination"
                @update:page="handlePageChange"
                @update:page-size="handlePageSizeChange"
                :row-key="row => row.id"
                @update:checked-row-keys="handleCheck"
                :bordered="true"
                :single-line="false"
                size="small"
                class="user-table"
              />
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </n-card>

    <!-- 用户编辑对话框 -->
    <n-modal v-model:show="showUserModal" :mask-closable="false">
      <n-card
        style="width: 600px"
        :title="editingUser.id ? '编辑用户' : '新增用户'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          :model="editingUser"
          :rules="userRules"
          ref="userForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          :style="{
            maxWidth: '640px'
          }"
        >
          <n-form-item label="用户名" path="username">
            <n-input v-model:value="editingUser.username" placeholder="请输入用户名" />
          </n-form-item>
          <n-form-item label="昵称" path="nickname">
            <n-input v-model:value="editingUser.nickname" placeholder="请输入昵称" />
          </n-form-item>
          <n-form-item label="密码" path="passwd">
            <n-input v-model:value="editingUser.passwd" type="password" placeholder="请输入密码" />
          </n-form-item>
          <n-form-item label="手机号" path="workMobile">
            <n-input v-model:value="editingUser.workMobile" placeholder="请输入手机号" />
          </n-form-item>
          <n-form-item label="所属部门" path="departmentId">
            <n-tree-select
              v-model:value="editingUser.departmentId"
              :options="departmentTree"
              placeholder="请选择所属部门"
            />
          </n-form-item>
          <n-form-item label="角色" path="roles">
            <n-select
              v-model:value="editingUser.roles"
              multiple
              :options="roleOptions"
              placeholder="请选择角色"
            />
          </n-form-item>
          <n-form-item label="状态" path="disabled">
            <n-switch v-model:value="editingUser.disabled" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="closeUserModal">取消</n-button>
            <n-button type="primary" @click="saveUser">保存</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>

    <!-- 部门编辑对话框 -->
    <n-modal v-model:show="showDepartmentModal" :mask-closable="false">
      <n-card
        style="width: 400px"
        :title="editingDepartment.key ? '编辑部门' : '新增部门'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          :model="editingDepartment"
          :rules="departmentRules"
          ref="departmentForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
        >
          <n-form-item label="部门名称" path="name">
            <n-input v-model:value="editingDepartment.name" placeholder="请输入部门名称" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showDepartmentModal = false">取消</n-button>
            <n-button type="primary" @click="saveDepartment">保存</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </n-space>
</template>

<style scoped>
/* 页面容器样式 */
.users-page-container {
  height: 100%;
  width: 100%;
}

.main-card {
  height: calc(100vh - 80px); /* 减去页面上下边距和头部高度 */
  overflow: hidden; /* 防止内容溢出 */
}

.main-grid {
  height: 100%;
}

.grid-item {
  height: 100%;
}

/* 左侧部门卡片和右侧用户卡片样式 */
.department-card, .users-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}

/* 可滚动容器样式 */
.scrollable-container {
  flex: 1;
  overflow-y: auto;
  height: calc(100% - 40px); /* 减去卡片标题高度 */
  min-height: 300px; /* 设置最小高度 */
  padding-bottom: 20px; /* 添加底部内边距，确保最后一项完全可见 */
  position: relative; /* 确保定位上下文 */
}

/* 表格容器特殊样式 */
.table-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保容器占满父元素高度 */
  padding-bottom: 40px; /* 为分页组件留出空间 */
  min-height: 400px; /* 确保表格容器有足够的高度 */
}

/* 树形容器特殊样式 */
.tree-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保容器占满父元素高度 */
}

/* 部门树样式 */
.department-tree {
  width: 100%;
  padding-bottom: 50px; /* 增加底部空间 */
  height: auto !important; /* 强制高度自适应内容 */
  min-height: 100%; /* 最小高度100% */
  display: block; /* 确保正确显示 */
  position: relative; /* 提供定位上下文 */
}

/* 用户表格样式 */
.user-table {
  width: 100%;
  position: relative;
  overflow: visible;
  min-height: 300px; /* 确保表格有足够的高度 */
  font-size: 14px;
}

/* 调整表格整体样式 */
.user-table :deep(.n-data-table-wrapper) {
  font-size: 14px;
}

/* 确保分页组件固定在右下角 */
.user-table :deep(.n-data-table-pagination) {
  position: absolute;
  bottom: -40px;
  right: 0;
  padding: 8px 14px;
  background-color: var(--n-merged-th-color);
  border-top: 1px solid var(--n-merged-border-color);
  z-index: 10;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
  width: auto;
  min-width: 300px;
  display: flex;
  justify-content: flex-end;
}

/* 确保表格内容在超出高度时内部垂直滚动 */
.user-table :deep(.n-data-table-base-table-body) {
  min-height: calc(70vh - 20px);
  max-height: calc(50vh - 20px);

  overflow-y: auto;
}


.user-table :deep(.n-data-table-th) {
  padding: 8px 12px !important;
  height: 40px !important;
  line-height: 1.5 !important;
}

/* 调整表头文字样式 */
.user-table :deep(.n-data-table-th__title) {
  font-weight: 600;
  font-size: 14px;
  text-align: left; /* 表头文字左对齐 */
}

/* 确保表头单元格内容左对齐 */
.user-table :deep(.n-data-table-th) {
  text-align: left !important;
}

.n-tree {
  --n-item-height: 40px;
  height: auto !important; /* 强制树组件高度自适应内容 */
  max-height: none !important; /* 移除最大高度限制 */
}

/* 确保树节点正确显示 */
.n-tree .n-tree-node-wrapper {
  width: 100%;
}

/* 确保树节点内容正确显示 */
.n-tree .n-tree-node-content {
  width: calc(100% - 24px); /* 减去左侧缩进 */
}

/* 修复树节点展开后的滚动问题 */
.n-tree-node--expanded + .n-tree-node-indent {
  height: auto !important;
  visibility: visible !important;
}

/* 确保滚动容器能够正确响应内容变化 */
.tree-container.scrollable-container {
  overflow-y: auto !important;
  height: auto !important;
  max-height: calc(100vh - 150px); /* 设置最大高度 */
}

/* 添加以下样式以调整操作列的布局 */
.n-data-table .n-button.n-button--quaternary {
  padding: 0;
  margin: 0 4px;
}

/* 确保表格内容单元格居中对齐 */
.user-table :deep(.n-data-table-td) {
  text-align: center !important;
}

/* 可以添加以下样式来调整展开/收起图标的大小 */
.n-tree .n-tree-node-switcher {
  transform: none !important;
}

/* 调整图标大小和对齐方式 */
.n-tree .n-tree-node-switcher svg {
  font-size: 18px;
  width: 1em;
  height: 1em;
}

/* 确保叶子节点没有左边 */
.n-tree .n-tree-node-content__prefix {
  width: auto;
}

/* 隐藏叶子节点的切换器 */
.n-tree .n-tree-node--leaf .n-tree-node-switcher {
  visibility: hidden;
  width: 0;
}

/* 修改节点操作按钮的样式 */
.n-tree .n-tree-node-content {
  position: relative;
}

.tree-node-action {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.n-tree-node:hover .tree-node-action,
.n-tree-node--selected .tree-node-action {
  opacity: 1;
}

.tree-node-action .n-icon {
  font-size: 16px;
}

/* 确保下拉菜单在树节点之上 */
.n-dropdown-menu {
  z-index: 1000;
}
</style>
