<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    :title="title"
    style="width: 100vw; height: 100vh; max-width: 100vw; max-height: 100vh"
    :mask-closable="false"
    :closable="true"
    @close="handleCancel"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="4" :x-gap="16" :y-gap="16">
        <n-grid-item>
          <n-form-item label="机构代码" path="id">
            <n-input
              :value="isEdit ? String(formData.id || '') : '系统自动生成'"
              :placeholder="isEdit ? '机构代码' : '系统自动生成'"
              readonly
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="机构名称" path="orgName">
            <n-input
              v-model:value="formData.orgName"
              placeholder="请输入机构名称"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="省/直辖市" path="province">
            <n-select
              v-model:value="formData.province"
              :options="provinceOptions"
              placeholder="请选择省/直辖市"
              @update:value="handleProvinceChange"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="市/区" path="city">
            <n-select
              v-model:value="formData.city"
              :options="filteredCityOptions"
              placeholder="请选择市/区"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="主营品牌" path="salesBrands">
            <n-select
              v-model:value="formData.salesBrands"
              :options="mainBrandOptions"
              placeholder="请选择主营品牌"
              multiple
              clearable
              max-tag-count="responsive"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="机构类型" path="orgType">
            <n-select
              v-model:value="formData.orgType"
              :options="orgTypeOptions"
              placeholder="请选择机构类型"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="联系人" path="contactPerson">
            <n-input
              v-model:value="formData.contactPerson"
              placeholder="请输入联系人姓名"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="联系电话" path="contactPhone">
            <n-input
              v-model:value="formData.contactPhone"
              placeholder="请输入联系电话"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item :span="2">
          <n-form-item label="业务权限" path="bizPermissions">
            <n-select
              v-model:value="formData.bizPermissions"
              :options="businessPermissionOptions"
              placeholder="请选择业务权限"
              multiple
              clearable
              max-tag-count="responsive"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item :span="2">
          <n-form-item label="详细地址" path="address">
            <n-input
              v-model:value="formData.address"
              placeholder="请输入详细地址"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? "更新" : "保存" }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import bizOrgApi from "@/api/bizOrg";
import messages from "@/utils/messages";

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增机构",
  },
  provinceOptions: {
    type: Array,
    default: () => [],
  },
  cityOptions: {
    type: Array,
    default: () => [],
  },
  mainBrandOptions: {
    type: Array,
    default: () => [],
  },
  businessPermissionOptions: {
    type: Array,
    default: () => [],
  },
  orgTypeOptions: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits(["update:visible", "save", "cancel"]);

// 状态变量
const formRef = ref(null);
const saving = ref(false);

// 表单数据
const formData = reactive({
  id: null,
  orgName: "",
  province: null,
  city: null,
  address: "",
  contactPerson: "",
  contactPhone: "",
  salesBrands: [],
  bizPermissions: [],
  orgType: null,
  status: "active",
});

// 计算属性：根据选中的省份过滤城市选项
const filteredCityOptions = computed(() => {
  if (!formData.province) {
    // 未选择省份时，只显示"请选择"占位符
    return [{ label: "请选择", value: null, disabled: true }];
  }

  // 选择省份后，显示对应的城市选项（不包含"不限"选项）
  const cityList = props.cityOptions.filter(
    (city) => city.value !== null && city.parent === formData.province
  );

  return [{ label: "请选择", value: null, disabled: true }, ...cityList];
});

// 表单验证规则
const rules = {
  orgName: [{ required: true, message: "请输入机构名称", trigger: "blur" }],
  province: [
    {
      required: true,
      message: "请选择省/直辖市",
      trigger: "change",
      validator: (_, value) => {
        if (!value || value === null) {
          return new Error("请选择省/直辖市");
        }
        return true;
      },
    },
  ],
  city: [
    {
      required: true,
      message: "请选择市/区",
      trigger: "change",
      validator: (_, value) => {
        if (!value || value === null) {
          return new Error("请选择市/区");
        }
        return true;
      },
    },
  ],
  // salesBrands 字段允许为空，移除必填校验
  orgType: [
    {
      required: true,
      message: "请选择机构类型",
      trigger: "change",
      validator: (_, value) => {
        if (!value || value === null) {
          return new Error("请选择机构类型");
        }
        return true;
      },
    },
  ],
  // bizPermissions 字段允许为空，移除必填校验
  contactPhone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

// 监听visible变化
const visible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 处理省份变化
const handleProvinceChange = () => {
  formData.city = null;
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    orgName: "",
    province: null,
    city: null,
    address: "",
    contactPerson: "",
    contactPhone: "",
    salesBrands: [],
    bizPermissions: [],
    orgType: null,
    status: "active",
  });

  if (formRef.value) {
    formRef.value.restoreValidation();
  }
};

// 设置表单数据
const setFormData = (data) => {
  // 处理主营品牌字段：兼容数组和逗号分割字符串格式
  let salesBrands = [];
  if (data.salesBrands) {
    if (Array.isArray(data.salesBrands)) {
      salesBrands = data.salesBrands;
    } else if (typeof data.salesBrands === "string") {
      salesBrands = data.salesBrands
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item);
    }
  }

  // 处理业务权限字段：兼容数组和逗号分割字符串格式
  let bizPermissions = [];
  if (data.bizPermissions) {
    if (Array.isArray(data.bizPermissions)) {
      bizPermissions = data.bizPermissions;
    } else if (typeof data.bizPermissions === "string") {
      bizPermissions = data.bizPermissions
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item);
    }
  }

  Object.assign(formData, {
    ...data,
    salesBrands,
    bizPermissions,
  });
};

// 处理保存
const handleSave = async () => {
  try {
    await formRef.value?.validate();

    saving.value = true;

    const submitData = {
      ...formData,
      // 将多选字段转换为逗号分割字符串格式
      salesBrands: Array.isArray(formData.salesBrands)
        ? formData.salesBrands.join(",")
        : formData.salesBrands,
      bizPermissions: Array.isArray(formData.bizPermissions)
        ? formData.bizPermissions.join(",")
        : formData.bizPermissions,
    };

    let response;
    if (props.isEdit) {
      response = await bizOrgApi.updateBizOrg(submitData);
    } else {
      const { id, ...createData } = submitData;
      response = await bizOrgApi.createBizOrg(createData);
    }

    if (response.code === 200) {
      messages.success(props.isEdit ? "更新成功" : "创建成功");
      emit("save");
      visible.value = false;
    } else {
      messages.error(response.message || "保存失败");
    }
  } catch (error) {
    console.error("保存失败:", error);
    if (error.message) {
      messages.error(error.message);
    }
  } finally {
    saving.value = false;
  }
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  visible.value = false;
};

// 暴露方法
defineExpose({
  resetForm,
  setFormData,
});
</script>

<style scoped>
/* 最大化弹窗样式 */
:deep(.n-modal) {
  margin: 0 !important;
  border-radius: 0 !important;
}

:deep(.n-card) {
  height: 100vh !important;
  border-radius: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

:deep(.n-card__content) {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: 24px !important;
}

:deep(.n-card__footer) {
  flex-shrink: 0 !important;
  padding: 16px 24px !important;
  border-top: 1px solid #f0f0f0 !important;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

/* 表单网格响应式调整 */
:deep(.n-grid) {
  width: 100%;
}

/* 多选下拉框样式优化 */
:deep(.n-select .n-base-selection .n-base-selection-tags) {
  max-height: 80px;
  overflow-y: auto;
}

:deep(.n-select-menu) {
  max-height: 300px;
}

/* 只读输入框样式 */
:deep(.n-input[readonly] .n-input__input-el) {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}
</style>
