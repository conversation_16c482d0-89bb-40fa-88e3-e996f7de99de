<template>
  <div class="center-div"></div>
</template>
<script setup>
import * as ww from '@wecom/jssdk'
import { onMounted } from 'vue'
import { doPost } from '@/utils/requests' // 导入封装的 doPost 方法

// 初始化登录组件


onMounted(() => {
    const wwLogin = ww.createWWLoginPanel({
        el: '.center-div',
        params: {
            login_type: 'CorpApp',
            appid: 'wwb5a4214455e4bf68',
            agentid: '1000002',
            redirect_uri: 'https://jwd.vooice.tech/wecom',
            state: 'loginState',
            redirect_type: 'callback',
        },
        onCheckWeComLogin({ isWeComLogin }) {
            console.log(isWeComLogin)
        },
        onLoginSuccess({ code }) {
            console.log({ code });
            // 使用封装的 doPost 方法发送请求
            doPost(`/system/wecom/login?code=${code}`)
              .then(response => {
                console.log('Login successful:', response);
                window.location.href = '/wecom-success'
              })
              .catch(error => {
                console.error('Login failed:', error);
              });
        },
        onLoginFail(err) {
            console.log(err)
        },
    })
})
</script>
<style scoped>
/* 确保整个页面不显示滚动条 */
html, body {
  height: 100%;
  overflow: hidden;
}

/* 添加样式以实现垂直和水平居中 */
.center-div {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* 使用视口高度确保居中对齐 */
}
</style>
