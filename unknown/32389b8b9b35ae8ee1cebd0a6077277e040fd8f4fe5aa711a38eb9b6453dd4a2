<template>
  <div class="test-index">
    <n-card title="测试页面导航">
      <n-space vertical size="large">
        <n-alert type="info" title="测试环境">
          这里是开发测试环境，包含各种组件和功能的测试页面。
        </n-alert>

        <n-grid :cols="2" :x-gap="24" :y-gap="16">
          <!-- QueryPage 相关测试 -->
          <n-grid-item>
            <n-card title="QueryPage 组件测试" hoverable>
              <template #header-extra>
                <n-tag type="success">新功能</n-tag>
              </template>
              <n-space vertical>
                <n-text>测试 QueryPage 通用查询页面组件的各种功能</n-text>
                <n-space>
                  <n-button type="primary" @click="goTo('/test/query-page')">
                    功能测试
                  </n-button>
                  <n-button type="info" @click="goTo('/examples/enhanced-query-page')">
                    增强演示
                  </n-button>
                </n-space>
                <n-collapse>
                  <n-collapse-item title="功能特性" name="features">
                    <n-ul>
                      <n-li>数字字段：千分位格式化</n-li>
                      <n-li>货币字段：带货币符号显示</n-li>
                      <n-li>字典字段：n-tag形式渲染</n-li>
                      <n-li>冻结列：横向滚动+固定列</n-li>
                      <n-li>多值字段：多个tag显示</n-li>
                      <n-li>字典缓存：自动缓存API数据</n-li>
                    </n-ul>
                  </n-collapse-item>
                </n-collapse>
              </n-space>
            </n-card>
          </n-grid-item>

          <!-- 业务组件测试 -->
          <n-grid-item>
            <n-card title="业务组件测试" hoverable>
              <n-space vertical>
                <n-text>测试各种业务相关的组件功能</n-text>
                <n-space>
                  <n-button @click="goTo('/test/biz-org-selector')">
                    机构选择器
                  </n-button>
                  <n-button @click="goTo('/examples/receivable-selector')">
                    应收选择器
                  </n-button>
                </n-space>
                <n-collapse>
                  <n-collapse-item title="组件列表" name="components">
                    <n-ul>
                      <n-li>BizOrgSelector：业务机构选择器</n-li>
                      <n-li>ReceivableSelector：应收账款选择器</n-li>
                      <n-li>CustomerSelector：客户选择器</n-li>
                      <n-li>GiftStockSelector：赠品选择器</n-li>
                    </n-ul>
                  </n-collapse-item>
                </n-collapse>
              </n-space>
            </n-card>
          </n-grid-item>

          <!-- 页面功能测试 -->
          <n-grid-item>
            <n-card title="页面功能测试" hoverable>
              <n-space vertical>
                <n-text>测试各种业务页面的功能</n-text>
                <n-space>
                  <n-button @click="goTo('/inventory/deposit-order')">
                    定金订单
                  </n-button>
                  <n-button @click="goTo('/app')">
                    应用首页
                  </n-button>
                </n-space>
                <n-collapse>
                  <n-collapse-item title="页面列表" name="pages">
                    <n-ul>
                      <n-li>定金订单管理：订单查询和管理</n-li>
                      <n-li>库存管理：车辆库存管理</n-li>
                      <n-li>财务管理：应收应付管理</n-li>
                    </n-ul>
                  </n-collapse-item>
                </n-collapse>
              </n-space>
            </n-card>
          </n-grid-item>

          <!-- 开发工具 -->
          <n-grid-item>
            <n-card title="开发工具" hoverable>
              <n-space vertical>
                <n-text>开发和调试相关的工具页面</n-text>
                <n-space>
                  <n-button @click="openDevTools">
                    开发者工具
                  </n-button>
                  <n-button @click="viewLogs">
                    查看日志
                  </n-button>
                </n-space>
                <n-collapse>
                  <n-collapse-item title="工具说明" name="tools">
                    <n-ul>
                      <n-li>开发者工具：浏览器开发者工具</n-li>
                      <n-li>控制台日志：查看应用运行日志</n-li>
                      <n-li>网络请求：监控API请求</n-li>
                      <n-li>性能分析：页面性能分析</n-li>
                    </n-ul>
                  </n-collapse-item>
                </n-collapse>
              </n-space>
            </n-card>
          </n-grid-item>
        </n-grid>

        <n-divider />

        <n-card title="快速访问">
          <n-space>
            <n-button-group>
              <n-button type="primary" @click="goTo('/examples/enhanced-query-page')">
                QueryPage 演示
              </n-button>
              <n-button @click="goTo('/test/query-page')">
                功能测试
              </n-button>
              <n-button @click="goTo('/test/biz-org-selector')">
                组件测试
              </n-button>
            </n-button-group>
          </n-space>
        </n-card>

        <n-card title="开发信息">
          <n-descriptions bordered :column="3">
            <n-descriptions-item label="Vue版本">
              {{ vueVersion }}
            </n-descriptions-item>
            <n-descriptions-item label="NaiveUI版本">
              {{ naiveVersion }}
            </n-descriptions-item>
            <n-descriptions-item label="开发模式">
              {{ isDev ? '是' : '否' }}
            </n-descriptions-item>
            <n-descriptions-item label="当前路由">
              {{ $route.path }}
            </n-descriptions-item>
            <n-descriptions-item label="页面标题">
              {{ $route.meta.title || '未设置' }}
            </n-descriptions-item>
            <n-descriptions-item label="构建时间">
              {{ buildTime }}
            </n-descriptions-item>
          </n-descriptions>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { version as vueVersion } from 'vue'
import { 
  NCard, NSpace, NAlert, NGrid, NGridItem, NButton, NButtonGroup,
  NText, NTag, NCollapse, NCollapseItem, NUl, NLi, NDivider,
  NDescriptions, NDescriptionsItem
} from 'naive-ui'
import messages from '@/utils/messages'

const router = useRouter()

// 版本信息
const naiveVersion = ref('2.34.x')
const isDev = ref(import.meta.env.DEV)
const buildTime = ref(new Date().toLocaleString())

const goTo = (path) => {
  router.push(path).catch(err => {
    console.error('路由跳转失败:', err)
    messages.error(`页面跳转失败: ${path}`)
  })
}

const openDevTools = () => {
  if (window.chrome && window.chrome.devtools) {
    messages.info('请按 F12 打开开发者工具')
  } else {
    messages.info('请按 F12 或右键选择"检查"打开开发者工具')
  }
}

const viewLogs = () => {
  console.log('=== 应用运行日志 ===')
  console.log('Vue版本:', vueVersion)
  console.log('开发模式:', isDev.value)
  console.log('当前时间:', new Date().toLocaleString())
  messages.success('日志已输出到控制台，请按F12查看')
}
</script>

<style scoped>
.test-index {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

:deep(.n-card) {
  height: 100%;
}

:deep(.n-card-header) {
  padding-bottom: 12px;
}
</style>
