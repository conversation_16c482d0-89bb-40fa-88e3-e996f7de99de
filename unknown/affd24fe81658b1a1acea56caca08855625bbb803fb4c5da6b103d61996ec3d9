<template>
  <div>
    <div class="section-title">
      <span class="title-text">产品信息</span>
      <n-button type="primary" size="small" @click="$emit('select-vehicle')">
        <template #icon>
          <n-icon>
            <component :is="CarOutlineIcon" />
          </n-icon>
        </template>
        选择车辆
      </n-button>
    </div>
    <n-divider title-placement="left"></n-divider>

    <!-- 车辆信息表单 -->
    <n-grid :cols="4" :x-gap="16" :y-gap="1">
      <n-grid-item>
        <n-form-item label="销售日期" path="dealDate" required>
          <n-date-picker v-model:value="form.dealDate" type="date" clearable style="width: 100%"
            value-format="timestamp" @update:value="val => form.dealDate = val" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="预计出库日期" path="expectedOutboundDate" required>
          <n-date-picker v-model:value="form.expectedOutboundDate" type="date" clearable style="width: 100%"
            value-format="timestamp" @update:value="val => form.expectedOutboundDate = val" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="出库单位" path="outboundOrgName" required>
          <department-selector v-model="selectedOutboundOrg" mode="single" label="选择出库单位" width="100%"
            @update:model-value="handleOutboundOrgChange" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item></n-grid-item>

      <n-grid-item>
        <n-form-item label="品牌" path="vehicleBrand">
          <n-input v-model:value="form.vehicleBrand" placeholder="车辆品牌" disabled />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="车型" path="vehicleSeries">
          <n-input v-model:value="form.vehicleSeries" placeholder="车辆车型" disabled />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="配置" path="vehicleConfig">
          <n-input v-model:value="form.vehicleConfig" placeholder="车辆配置" disabled />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="颜色代码" path="vehicleColorCode">
          <n-input v-model:value="form.vehicleColorCode" placeholder="颜色代码" disabled />
        </n-form-item>
      </n-grid-item>

      <!-- 金额信息 -->
      <n-grid-item>
        <n-form-item label="启票价格(元)" path="vehicleSbPrice">
          <n-input-number v-model:value="form.vehicleSbPrice" disabled style="width: 100%" :precision="2"
            :show-button="false" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="销售价格(元)" path="vehicleSalePrice">
          <n-input-number v-model:value="form.vehicleSalePrice" placeholder="请输入销售价格" style="width: 100%"
            :precision="2" :min="0" button-placement="both" @update:value="$emit('sale-price-change')" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="优惠金额(元)" path="discountAmount">
          <n-input-number v-model:value="form.discountAmount" placeholder="请输入优惠金额" style="width: 100%" :precision="2"
            :min="0" button-placement="both" @update:value="$emit('discount-change')" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="销售费用(元)" path="salesExpense">
          <n-input-number v-model:value="form.salesExpense" placeholder="请输入销售费用" style="width: 100%" :precision="2"
            :min="0" button-placement="both" @update:value="$emit('sales-expense-change')" />
        </n-form-item>
      </n-grid-item>

      <!-- 第二行：成交总价、成交总价（大写）、预估毛利润、预估毛利率 -->
      <n-grid-item>
        <n-form-item label="成交价格(元)" path="finalPrice" required>
          <div style="display: flex; align-items: center; width: 100%;">
            <n-input-number v-model:value="form.finalPrice" disabled style="flex: 1;" :precision="2"
              :show-button="false" />
            <span class="price-in-wan" style="margin-left: 10px; white-space: nowrap; width: 80px;">{{ finalPriceInWan }}</span>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="成交价格（大写）" path="finalPriceChinese">
          <n-input v-model:value="form.finalPriceChinese" disabled style="width: 100%"
            :autosize="{ minRows: 1, maxRows: 3 }" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="预估毛利润(元)" path="profitAmount">
          <n-input-number v-model:value="form.profitAmount" disabled style="width: 100%" :precision="2"
            :show-button="false"
            :status="form.profitAmount < 0 ? 'error' : (form.profitAmount > 0 ? 'success' : 'warning')" />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="预估毛利率(%)" path="profitRate">
          <n-input-number v-model:value="form.profitRate" disabled style="width: 100%" :precision="2"
            :show-button="false"
            :status="form.profitRate > 20 ? 'success' : (form.profitRate > 0 ? 'warning' : 'error')" />
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { markRaw } from 'vue'
import { CarOutline } from '@vicons/ionicons5'
import DepartmentSelector from '@/components/users/DepartmentSelector.vue'

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const CarOutlineIcon = markRaw(CarOutline)

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  },
  finalPriceInWan: {
    type: String,
    required: true
  },
  selectedOutboundOrg: {
    type: Object,
    default: null
  }
})

// 定义组件事件
const emit = defineEmits([
  'select-vehicle', 
  'sale-price-change', 
  'discount-change', 
  'sales-expense-change',
  'update:selectedOutboundOrg'
])

// 处理出库单位选择
const handleOutboundOrgChange = (org) => {
  emit('update:selectedOutboundOrg', org)
  
  if (org) {
    props.form.outboundOrgId = org.id
    props.form.outboundOrgName = org.name
  } else {
    props.form.outboundOrgId = null
    props.form.outboundOrgName = ''
  }
}
</script>
