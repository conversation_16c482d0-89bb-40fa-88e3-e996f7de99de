/* 组织机构树布局组件样式 */

.org-tree-settings-layout {
  width: 100%;
  height: 100%;

  // 主卡片样式
  .main-card {
    height: 100%;
    padding: 0;

    :deep(.n-card__content) {
      padding: 16px;
    }
  }

  // 数据列表卡片样式
  .data-list-card {
    height: calc(95vh - 32px);
    /* 固定高度 */
    display: flex;
    flex-direction: column;

    :deep(.n-card__content) {
      padding: 0;
      display: flex;
      flex-direction: column;
    }
  }

  // 数据表格容器样式
  .data-table-container {
    flex: 1;
    /* 使用flex布局自动填充剩余空间 */
    overflow: hidden;
    /* 修改为hidden，让内部表格处理滚动 */
    height: calc(90vh - 120px);
    /* 调整高度，减去标题和分页的高度，确保最后一行完全显示 */
    padding-bottom: 8px;
    /* 添加底部内边距，确保最后一行有足够空间 */

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #aaa;
    }

    // 表格样式
    :deep(.n-data-table) {

      // 表头居中对齐
      .n-data-table-th {
        text-align: center !important;

        .n-data-table-th__title {
          justify-content: center;
        }
      }

      // 表格数据居左对齐
      .n-data-table-td {
        text-align: left !important;
        padding: 8px 12px !important;
        /* 调整单元格内边距 */
        vertical-align: middle;
        /* 垂直居中对齐 */

        // 操作列居中对齐
        &.n-data-table-td--last {
          text-align: center !important;

          .n-space {
            justify-content: center;
          }
        }
      }

      // 表格行样式
      .n-data-table-tr {
        height: auto !important;
        /* 允许行高自适应内容 */
        min-height: 48px;
        /* 设置最小行高 */
      }

      // 操作按钮样式
      .n-button.n-button--quaternary {
        padding: 0;
        margin: 0 4px;
      }
    }
  }

  // 分页容器样式
  .pagination-container {
    padding: 8px 0;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #f0f0f0;
    min-height: 48px;
    /* 设置最小高度，确保分页组件有足够空间 */
  }

  // 新行高亮样式
  :deep(.new-row-highlight) {
    background-color: rgba(0, 128, 0, 0.1) !important;
    /* 浅绿色背景 */
    transition: background-color 0.5s ease;
    /* 平滑过渡效果 */

    &:hover {
      background-color: rgba(0, 128, 0, 0.15) !important;
    }
  }
}