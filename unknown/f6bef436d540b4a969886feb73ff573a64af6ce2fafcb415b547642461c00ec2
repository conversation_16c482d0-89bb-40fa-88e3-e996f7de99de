<template>
  <div>
    <div class="section-title">
      <span class="title-text">车辆保险</span>
    </div>
    <n-divider title-placement="left"></n-divider>

    <div class="option-row">
      <span class="option-label">客户是否在我司购置了车辆保险？</span>
      <n-radio-group v-model:value="form.hasInsurance">
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span class="option-tip" v-if="form.hasInsurance === 'YES'">
        客户已选择购买车辆保险，系统将推送订单给保险专员处理
      </span>
    </div>
  </div>
</template>

<script setup>
// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})
</script>
