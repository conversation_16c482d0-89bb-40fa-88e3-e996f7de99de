/**
 * QueryPage 配置工厂函数
 * 用于快速生成常见场景的配置
 */

import { h } from 'vue'
import { NIcon, NTag } from 'naive-ui'
import { EyeOutline, CreateOutline, TrashOutline, CopyOutline } from '@vicons/ionicons5'

/**
 * 创建基础页面配置
 * @param {Object} options 配置选项
 * @returns {Object} 页面配置
 */
export function createPageConfig(options = {}) {
  return {
    rowKey: options.rowKey || 'id',
    scrollX: options.scrollX || 1200,
    search: options.search !== false,
    searchPlaceholder: options.searchPlaceholder || '请输入关键词',
    searchWidth: options.searchWidth || '300px',
    selection: options.selection !== false,
    actions: {
      add: options.addEnable !== false,
      addText: options.addText || '新增',
      edit: options.editEnable !== false,
      delete: options.deleteEnable !== false,
      view: options.viewEnable !== false,
      ...options.actions
    },
    buttons: options.buttons || [],
    actionWidth: options.actionWidth || 120,
    ...options
  }
}

/**
 * 创建字段配置
 * @param {Array} fields 字段定义数组
 * @returns {Array} 字段配置
 */
export function createFieldConfig(fields) {
  return fields.map(field => ({
    name: field.name,
    label: field.label || field.name,
    type: field.type || 'string',
    filter: field.filter || false,
    table: field.table !== false,
    width: field.width,
    fixed: field.fixed,
    align: field.align || 'center',
    sortable: field.sortable || false,
    ellipsis: field.ellipsis !== false,
    options: field.options || [],
    defaultValue: field.defaultValue,
    span: field.span || 8,
    render: field.render,
    ...field
  }))
}

/**
 * 创建常用字段类型
 */
export const FieldTypes = {
  /**
   * ID字段
   */
  id: (options = {}) => ({
    name: 'id',
    label: 'ID',
    type: 'number',
    filter: false,
    table: true,
    width: 80,
    fixed: 'left',
    sortable: true,
    ...options
  }),

  /**
   * 名称字段
   */
  name: (options = {}) => ({
    name: 'name',
    label: '名称',
    type: 'string',
    filter: true,
    table: true,
    width: 150,
    ...options
  }),

  /**
   * 状态字段
   */
  status: (options = {}) => ({
    name: 'status',
    label: '状态',
    type: 'dict',
    filter: true,
    table: true,
    width: 100,
    options: [
      { label: '全部', value: null },
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    render: (row) => {
      const statusMap = {
        1: { text: '启用', type: 'success' },
        0: { text: '禁用', type: 'error' }
      }
      const status = statusMap[row.status]
      return status ? h(NTag, { type: status.type, size: 'small' }, () => status.text) : '-'
    },
    ...options
  }),

  /**
   * 日期字段
   */
  date: (name, label, options = {}) => ({
    name,
    label,
    type: 'date',
    filter: true,
    table: true,
    width: 120,
    ...options
  }),

  /**
   * 数字字段
   */
  number: (name, label, options = {}) => ({
    name,
    label,
    type: 'number',
    filter: true,
    table: true,
    width: 120,
    align: 'center',
    sortable: true,
    decimals: options.decimals || 0,
    ...options
  }),

  /**
   * 货币字段
   */
  currency: (name, label, options = {}) => ({
    name,
    label,
    type: 'currency',
    filter: true,
    table: true,
    width: 140,
    align: 'right',
    sortable: true,
    currency: options.currency || '¥',
    decimals: options.decimals || 2,
    ...options
  }),

  /**
   * 字典字段
   */
  dict: (name, label, dictKey, options = {}) => ({
    name,
    label,
    type: 'dict',
    filter: true,
    table: true,
    width: 120,
    dictKey, // 字典key
    renderAsTag: options.renderAsTag !== false, // 默认渲染为tag
    multiple: options.multiple || false, // 是否多选
    ...options
  }),

  /**
   * 金额字段（兼容旧版本）
   */
  amount: (name, label, options = {}) => ({
    name,
    label,
    type: 'currency',
    filter: false,
    table: true,
    width: 140,
    align: 'right',
    sortable: true,
    currency: '¥',
    decimals: 2,
    ...options
  }),

  /**
   * VIN字段
   */
  vin: (options = {}) => ({
    name: 'vin',
    label: 'VIN',
    type: 'string',
    filter: false,
    table: true,
    width: 200,
    frozen: true, // 使用新的frozen属性代替fixed
    align: 'center',
    render: (row) => {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            padding: '4px 0',
            fontFamily: 'Monaco, "Courier New", monospace',
            fontSize: '13px'
          },
          onClick: () => copyToClipboard(row.vin),
          title: '点击复制VIN'
        },
        [
          h('span', {
            style: {
              marginRight: '5px',
              fontFamily: 'Monaco, "Courier New", monospace',
              letterSpacing: '0.5px'
            }
          }, row.vin),
          h(NIcon, {
            size: 16,
            color: 'var(--primary-color)',
            style: { opacity: 0.8 }
          }, () => h(CopyOutline))
        ]
      )
    },
    ...options
  }),

  /**
   * 冻结字段（通用）
   */
  frozen: (name, label, type = 'string', options = {}) => ({
    name,
    label,
    type,
    filter: options.filter !== false,
    table: true,
    width: options.width || 150,
    frozen: true, // 冻结列
    align: options.align || 'center',
    sortable: options.sortable || false,
    ...options
  }),

  /**
   * 操作列
   */
  actions: (options = {}) => ({
    name: 'actions',
    label: '操作',
    type: 'custom',
    filter: false,
    table: true,
    width: options.width || 120,
    fixed: 'right',
    align: 'center',
    render: (row, _, { emit }) => {
      const actions = []

      if (options.view !== false) {
        actions.push(
          h('div', {
            style: {
              cursor: 'pointer',
              color: '#2080f0',
              fontSize: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            },
            onClick: () => emit('view', row)
          }, [h(NIcon, { size: 20 }, () => h(EyeOutline))])
        )
      }

      if (options.edit !== false) {
        actions.push(
          h('div', {
            style: {
              cursor: 'pointer',
              color: '#18a058',
              fontSize: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            },
            onClick: () => emit('edit', row)
          }, [h(NIcon, { size: 20 }, () => h(CreateOutline))])
        )
      }

      if (options.delete !== false) {
        actions.push(
          h('div', {
            style: {
              cursor: 'pointer',
              color: '#d03050',
              fontSize: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            },
            onClick: () => emit('delete', row)
          }, [h(NIcon, { size: 20 }, () => h(TrashOutline))])
        )
      }

      return h('div', {
        style: {
          display: 'flex',
          justifyContent: 'center',
          gap: '12px'
        }
      }, actions)
    },
    ...options
  })
}

/**
 * 创建常用按钮配置
 */
export const ButtonTypes = {
  export: (options = {}) => ({
    key: 'export',
    text: '导出',
    type: 'info',
    icon: 'DownloadOutline',
    ...options
  }),

  import: (options = {}) => ({
    key: 'import',
    text: '导入',
    type: 'info',
    icon: 'CloudUploadOutline',
    ...options
  }),

  batchDelete: (options = {}) => ({
    key: 'batchDelete',
    text: '批量删除',
    type: 'error',
    icon: 'TrashOutline',
    requireSelection: true,
    ...options
  }),

  refresh: (options = {}) => ({
    key: 'refresh',
    text: '刷新',
    type: 'default',
    icon: 'RefreshOutline',
    ...options
  })
}

/**
 * 复制到剪贴板工具函数
 */
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(() => {
    // 这里可以添加成功提示
    console.log('复制成功:', text)
  }).catch(() => {
    // 这里可以添加失败提示
    console.error('复制失败')
  })
}

/**
 * 创建完整的查询页面配置
 * @param {Object} options 配置选项
 * @returns {Object} 完整配置
 */
export function createQueryPageConfig(options) {
  const {
    pageConfig: pageOptions = {},
    fields = [],
    buttons = [],
    apiService,
    ...rest
  } = options

  return {
    pageConfig: createPageConfig(pageOptions),
    fieldConfig: createFieldConfig(fields),
    apiService,
    ...rest
  }
}

/**
 * 预设配置模板
 */
export const Templates = {
  /**
   * 基础CRUD页面
   */
  basicCrud: (entity, options = {}) => ({
    pageConfig: createPageConfig({
      searchPlaceholder: `请输入${entity}名称`,
      ...options.pageConfig
    }),
    fieldConfig: createFieldConfig([
      FieldTypes.id(),
      FieldTypes.name({ name: 'name', label: `${entity}名称` }),
      FieldTypes.status(),
      FieldTypes.date('createTime', '创建时间'),
      FieldTypes.actions()
    ]),
    ...options
  }),

  /**
   * 库存管理页面
   */
  inventory: (options = {}) => ({
    pageConfig: createPageConfig({
      searchPlaceholder: '请输入VIN或车型',
      buttons: [
        ButtonTypes.export(),
        ButtonTypes.import()
      ],
      ...options.pageConfig
    }),
    fieldConfig: createFieldConfig([
      FieldTypes.vin(),
      { name: 'model', label: '车型', type: 'string', filter: true, table: true, width: 150 },
      FieldTypes.status({ name: 'stockStatus', label: '库存状态' }),
      FieldTypes.amount('price', '价格'),
      FieldTypes.date('inTime', '入库时间'),
      FieldTypes.actions()
    ]),
    ...options
  })
}
