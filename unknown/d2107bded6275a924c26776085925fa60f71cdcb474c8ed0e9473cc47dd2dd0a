import { createApp } from 'vue'
import { createPinia } from 'pinia'
import naive from 'naive-ui'
import App from '@/App.vue'
import router from '@/router'
import './assets/styles/global.css'
import './styles/naive-ui-override.scss'
import { initAuthEventHandlers } from '@/utils/authHandler'
import tipPosition from '@/directives/tipPosition'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(naive)

// 注册自定义指令
app.directive('tip-position', tipPosition)

// 初始化认证事件处理器
initAuthEventHandlers()

app.mount('#app')
