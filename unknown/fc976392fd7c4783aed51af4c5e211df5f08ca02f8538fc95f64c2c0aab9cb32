/* 全局变量 */
:root {
  /* 主题色 */
  --primary-color: #18a058;
  --primary-hover: #36ad6a;
  --primary-active: #0c7a43;
  --primary-bg: #e6f7e6;

  /* 中性色 */
  --border-color: #dcdfe6;
  --text-color: #606266;

  /* 圆角 */
  --border-radius: 4px;
}

/* Naive UI 组件全局样式覆盖 */
.n-radio-group {
  display: flex;
  gap: 8px;
}

/* 隐藏 radio-group 的分隔线 */
.n-radio-group__splitor {
  display: none !important;
}

.n-radio {
  display: none !important;
}

.n-radio-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 16px;
  font-size: 14px;
  border: 1px solid var(--border-color);
  border-radius: 0 !important;
  color: var(--text-color);
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  margin-right: -1px;
  text-align: center;
}

.n-radio-button::after {
  display: none !important;
}

.n-radio-group .n-radio-button:first-child {
  border-radius: 0 !important;
}

.n-radio-group .n-radio-button:not(:first-child):not(:last-child) {
  border-radius: 0 !important;
}

.n-radio-group .n-radio-button:last-child {
  border-radius: 0 !important;
}

.n-radio-button:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
  z-index: 1;
}

.n-radio-button--checked {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  z-index: 2;
}

.n-radio-button--checked:hover {
  background-color: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
}

.n-radio-button--disabled {
  cursor: not-allowed;
  color: #c0c4cc !important;
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
}

.n-radio-button--checked.n-radio-button--disabled {
  color: #fff !important;
  background-color: #a0cfb4 !important;
  border-color: #a0cfb4 !important;
}

/* 数字选择器样式 */
.n-input-number {
  width: 100%;
}

.n-input-number .n-input__input {
  text-align: center !important;
}

.n-input-number-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 100%;
  color: var(--text-color);
  background-color: #f5f7fa;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s;
}

.n-input-number-button:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.n-input-number-button:active {
  background-color: var(--primary-bg);
}

.n-input-number-button--disabled {
  cursor: not-allowed;
  color: #c0c4cc;
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.n-input-number .n-input-wrapper {
  display: flex !important;
}

.n-input-number .n-input__prefix,
.n-input-number .n-input__suffix {
  margin: 0 !important;
  padding: 0 !important;
}

/* 基础样式 */
body {
  margin: 0;
  padding: 0;
  /* 移除字体定义，使用App.vue中的全局字体定义 */
}

.no-scroll {
  overflow: hidden;
  height: 100vh;
}

/* 确保按钮内的所有内容居中 */
.n-radio-button>* {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

/* 覆盖n-layout-scroll-container的样式，禁用垂直滚动 */
.n-layout-scroll-container {
  overflow-y: hidden !important;
}