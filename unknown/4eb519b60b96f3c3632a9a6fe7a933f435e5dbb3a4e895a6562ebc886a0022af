<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">车辆保险</span>
    </div>
    <n-divider class="section-divider" style="height: 2px; background-image: linear-gradient(to right, var(--primary-color, #18a058) 0%, rgba(24, 160, 88, 0.1) 100%); border: none;"></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">客户是否在我司购置了车辆保险？</span>
      <n-radio-group v-model:value="form.hasInsurance">
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span class="option-tip" v-if="form.hasInsurance === 'YES'" v-tip-position style="color: #18a058 !important;">
        客户已选择购买车辆保险，系统将推送订单给保险专员处理
      </span>
    </div>
  </div>
</template>

<script setup>
import { NDivider, NRadioGroup, NRadioButton } from 'naive-ui'

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})
</script>


