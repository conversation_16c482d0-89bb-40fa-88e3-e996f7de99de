<template>
  <div class="dict-page">
    <n-grid :cols="24" :x-gap="12">
      <!-- 左侧字典列表 -->
      <n-grid-item :span="8">
        <n-card title="业务字典" class="dict-list-card">
          <template #header-extra>
            <n-button type="primary" size="small" @click="showAddDictDialog" round>
              <template #icon>
                <n-icon><AddOutline /></n-icon>
              </template>
              新增字典
            </n-button>
          </template>

          <!-- 搜索框 -->
          <div class="search-box">
            <n-input v-model:value="dictSearchKeyword" placeholder="搜索字典" clearable>
              <template #prefix>
                <n-icon><SearchOutline /></n-icon>
              </template>
            </n-input>
          </div>

          <!-- 字典列表 -->
          <div class="dict-list">
            <n-spin :show="dictLoading">
              <n-list hoverable clickable>
                <n-list-item v-for="dict in filteredDictList" :key="dict.dict_code"
                  :class="{ 'active-dict': currentDict && currentDict.dict_code === dict.dict_code }"
                  @click="selectDict(dict)">
                  <div class="dict-item-container">
                    <n-thing :title="dict.dict_name" :description="dict.dict_code">
                      <template #description>
                        <div class="dict-code">{{ dict.dict_code }}</div>
                        <div class="dict-remark" v-if="dict.remark">{{ dict.remark }}</div>
                      </template>
                    </n-thing>
                    <div class="dict-actions">
                      <n-button circle secondary size="small" @click.stop="showEditDictDialog(dict)" class="action-btn">
                        <template #icon>
                          <n-icon><PencilOutline /></n-icon>
                        </template>
                      </n-button>
                      <n-button circle secondary size="small" type="error" @click.stop="confirmDeleteDict(dict)" class="action-btn">
                        <template #icon>
                          <n-icon><TrashOutline /></n-icon>
                        </template>
                      </n-button>
                    </div>
                  </div>
                </n-list-item>
                <n-empty v-if="filteredDictList.length === 0" description="暂无数据" />
              </n-list>
            </n-spin>
          </div>
        </n-card>
      </n-grid-item>

      <!-- 右侧字典值列表 -->
      <n-grid-item :span="16">
        <n-card :title="currentDict ? `${currentDict.dict_name} - 字典值` : '字典值'" class="dict-values-card">
          <template #header-extra>
            <n-button v-if="currentDict" type="primary" size="small" @click="showAddOptionDialog" round>
              <template #icon>
                <n-icon><AddOutline /></n-icon>
              </template>
              新增字典值
            </n-button>
          </template>

          <!-- 搜索框 -->
          <div class="search-box" v-if="currentDict">
            <n-input v-model:value="optionSearchKeyword" placeholder="搜索字典值" clearable>
              <template #prefix>
                <n-icon><SearchOutline /></n-icon>
              </template>
            </n-input>
          </div>

          <!-- 字典值表格 -->
          <div class="dict-values">
            <n-spin :show="optionsLoading">
              <n-data-table
                v-if="currentDict"
                :columns="optionsColumns"
                :data="filteredOptions"
                :pagination="optionsPagination"
                :row-key="row => row.option_value"
              />
              <n-empty v-else description="请选择左侧字典" />
            </n-spin>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 新增/编辑字典对话框 -->
    <n-modal
      v-model:show="dictDialogVisible"
      :title="isEditDict ? '编辑字典' : '新增字典'"
      preset="card"
      :style="{ width: '500px' }"
      :mask-closable="false"
    >
      <n-form
        ref="dictFormRef"
        :model="dictForm"
        :rules="dictRules"
        label-placement="left"
        label-width="80"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="字典编码" path="dict_code">
          <n-input v-model:value="dictForm.dict_code" placeholder="请输入字典编码"
            :disabled="isEditDict" />
        </n-form-item>
        <n-form-item label="字典名称" path="dict_name">
          <n-input v-model:value="dictForm.dict_name" placeholder="请输入字典名称" />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input v-model:value="dictForm.remark" type="textarea" placeholder="请输入备注" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dictDialogVisible = false">取消</n-button>
          <n-button type="primary" @click="saveDict" :loading="dictSaving">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 新增/编辑字典值对话框 -->
    <n-modal
      v-model:show="optionDialogVisible"
      :title="isEditOption ? '编辑字典值' : '新增字典值'"
      preset="card"
      :style="{ width: '500px' }"
      :mask-closable="false"
    >
      <n-form
        ref="optionFormRef"
        :model="optionForm"
        :rules="optionRules"
        label-placement="left"
        label-width="80"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="选项值" path="option_value">
          <n-input v-model:value="optionForm.option_value" placeholder="请输入选项值"
            :disabled="isEditOption" />
        </n-form-item>
        <n-form-item label="选项标签" path="option_label">
          <n-input v-model:value="optionForm.option_label" placeholder="请输入选项标签" />
        </n-form-item>
        <n-form-item label="排序" path="sort">
          <n-input-number v-model:value="optionForm.sort" placeholder="请输入排序" />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input v-model:value="optionForm.remark" type="textarea" placeholder="请输入备注" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="optionDialogVisible = false">取消</n-button>
          <n-button type="primary" @click="saveOption" :loading="optionSaving">保存</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, h, computed, reactive } from 'vue'
import { useDialog, useMessage } from 'naive-ui'
import {
  RefreshOutline, AddOutline, TrashOutline, PencilOutline, SearchOutline
} from '@vicons/ionicons5'
import {
  NButton, NSpace, NIcon, NDataTable, NModal, NForm, NFormItem, NInput,
  NInputNumber, NCard, NGrid, NGridItem, NList, NListItem, NThing, NSpin, NEmpty
} from 'naive-ui'
import {
  getDictList, getDictDetail, getDictOptions, createDict, updateDict, deleteDict,
  createDictOption, updateDictOption, deleteDictOption
} from '@/api/dict'

// 消息和对话框
const message = useMessage()
const dialog = useDialog()

// 字典列表相关
const dictList = ref([])
const dictLoading = ref(false)
const dictSearchKeyword = ref('')
const currentDict = ref(null)

// 字典值相关
const dictOptions = ref([])
const optionsLoading = ref(false)
const optionSearchKeyword = ref('')

// 字典表单相关
const dictDialogVisible = ref(false)
const isEditDict = ref(false)
const dictSaving = ref(false)
const dictFormRef = ref(null)
const dictForm = ref({
  dict_code: '',
  dict_name: '',
  remark: ''
})

// 字典值表单相关
const optionDialogVisible = ref(false)
const isEditOption = ref(false)
const optionSaving = ref(false)
const optionFormRef = ref(null)
const optionForm = ref({
  option_value: '',
  option_label: '',
  sort: 0,
  remark: ''
})

// 表单验证规则
const dictRules = {
  dict_code: [
    { required: true, message: '请输入字典编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '字典编码只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  dict_name: [
    { required: true, message: '请输入字典名称', trigger: 'blur' }
  ]
}

const optionRules = {
  option_value: [
    { required: true, message: '请输入选项值', trigger: 'blur' }
  ],
  option_label: [
    { required: true, message: '请输入选项标签', trigger: 'blur' }
  ]
}

// 字典值表格列配置
const optionsColumns = [
  {
    title: '选项值',
    key: 'option_value',
    width: 150
  },
  {
    title: '选项标签',
    key: 'option_label',
    width: 200
  },
  {
    title: '排序',
    key: 'sort',
    width: 100
  },
  {
    title: '备注',
    key: 'remark',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
    render(row) {
      return h(NSpace, {}, {
        default: () => [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              onClick: () => showEditOptionDialog(row)
            },
            {
              default: () => '编辑',
              icon: () => h(NIcon, null, { default: () => h(PencilOutline) })
            }
          ),
          h(
            NButton,
            {
              size: 'small',
              type: 'error',
              onClick: () => confirmDeleteOption(row)
            },
            {
              default: () => '删除',
              icon: () => h(NIcon, null, { default: () => h(TrashOutline) })
            }
          )
        ]
      })
    }
  }
]

// 字典值分页配置
const optionsPagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    optionsPagination.page = page
  },
  onUpdatePageSize: (pageSize) => {
    optionsPagination.pageSize = pageSize
    optionsPagination.page = 1
  }
})

// 过滤后的字典列表
const filteredDictList = computed(() => {
  if (!dictSearchKeyword.value) return dictList.value

  const keyword = dictSearchKeyword.value.toLowerCase()
  return dictList.value.filter(dict =>
    dict.dict_code.toLowerCase().includes(keyword) ||
    dict.dict_name.toLowerCase().includes(keyword) ||
    (dict.remark && dict.remark.toLowerCase().includes(keyword))
  )
})

// 过滤后的字典值列表
const filteredOptions = computed(() => {
  if (!optionSearchKeyword.value) return dictOptions.value

  const keyword = optionSearchKeyword.value.toLowerCase()
  return dictOptions.value.filter(option =>
    option.option_value.toLowerCase().includes(keyword) ||
    option.option_label.toLowerCase().includes(keyword) ||
    (option.remark && option.remark.toLowerCase().includes(keyword))
  )
})

// 页面加载时获取字典列表
onMounted(() => {
  fetchDictList()
})

// 获取字典列表
const fetchDictList = async () => {
  dictLoading.value = true
  try {
    const res = await getDictList()
    dictList.value = res.data || []
  } catch (error) {
    console.error('获取字典列表失败:', error)
    message.error('获取字典列表失败')
  } finally {
    dictLoading.value = false
  }
}

// 选择字典
const selectDict = async (dict) => {
  currentDict.value = dict
  await fetchDictOptions(dict.dict_code)
}

// 获取字典值列表
const fetchDictOptions = async (dictCode) => {
  if (!dictCode) return

  optionsLoading.value = true
  try {
    const res = await getDictOptions(dictCode)
    dictOptions.value = res.data || []
  } catch (error) {
    console.error('获取字典值列表失败:', error)
    message.error('获取字典值列表失败')
  } finally {
    optionsLoading.value = false
  }
}

// 显示新增字典对话框
const showAddDictDialog = () => {
  isEditDict.value = false
  dictForm.value = {
    dict_code: '',
    dict_name: '',
    remark: ''
  }
  dictDialogVisible.value = true
}

// 显示编辑字典对话框
const showEditDictDialog = (dict) => {
  isEditDict.value = true
  dictForm.value = {
    dict_code: dict.dict_code,
    dict_name: dict.dict_name,
    remark: dict.remark || ''
  }
  dictDialogVisible.value = true
}

// 保存字典
const saveDict = () => {
  dictFormRef.value?.validate(async (errors) => {
    if (errors) return

    dictSaving.value = true
    try {
      if (isEditDict.value) {
        await updateDict(dictForm.value)
        message.success('字典更新成功')

        // 更新本地列表中的数据
        const index = dictList.value.findIndex(item => item.dict_code === dictForm.value.dict_code)
        if (index !== -1) {
          dictList.value[index] = { ...dictList.value[index], ...dictForm.value }

          // 如果当前选中的是被编辑的字典，也需要更新
          if (currentDict.value && currentDict.value.dict_code === dictForm.value.dict_code) {
            currentDict.value = { ...currentDict.value, ...dictForm.value }
          }
        }
      } else {
        await createDict(dictForm.value)
        message.success('字典创建成功')

        // 添加到本地列表
        dictList.value.push(dictForm.value)
      }

      dictDialogVisible.value = false
    } catch (error) {
      console.error('保存字典失败:', error)
      message.error('保存字典失败')
    } finally {
      dictSaving.value = false
    }
  })
}

// 确认删除字典
const confirmDeleteDict = (dict) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除字典 "${dict.dict_name}" 吗？删除后将无法恢复，且会同时删除所有字典值。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => deleteSelectedDict(dict)
  })
}

// 删除字典
const deleteSelectedDict = async (dict) => {
  dictLoading.value = true
  try {
    await deleteDict(dict.dict_code)
    message.success('字典删除成功')

    // 从本地列表中移除
    dictList.value = dictList.value.filter(item => item.dict_code !== dict.dict_code)

    // 如果当前选中的是被删除的字典，清空选择
    if (currentDict.value && currentDict.value.dict_code === dict.dict_code) {
      currentDict.value = null
      dictOptions.value = []
    }
  } catch (error) {
    console.error('删除字典失败:', error)
    message.error('删除字典失败')
  } finally {
    dictLoading.value = false
  }
}

// 显示新增字典值对话框
const showAddOptionDialog = () => {
  if (!currentDict.value) {
    message.warning('请先选择字典')
    return
  }

  isEditOption.value = false
  optionForm.value = {
    option_value: '',
    option_label: '',
    sort: dictOptions.value.length + 1,
    remark: ''
  }
  optionDialogVisible.value = true
}

// 显示编辑字典值对话框
const showEditOptionDialog = (option) => {
  isEditOption.value = true
  optionForm.value = {
    option_value: option.option_value,
    option_label: option.option_label,
    sort: option.sort || 0,
    remark: option.remark || ''
  }
  optionDialogVisible.value = true
}

// 保存字典值
const saveOption = () => {
  if (!currentDict.value) {
    message.warning('请先选择字典')
    return
  }

  optionFormRef.value?.validate(async (errors) => {
    if (errors) return

    optionSaving.value = true
    try {
      if (isEditOption.value) {
        await updateDictOption(currentDict.value.dict_code, optionForm.value)
        message.success('字典值更新成功')

        // 更新本地列表中的数据
        const index = dictOptions.value.findIndex(item => item.option_value === optionForm.value.option_value)
        if (index !== -1) {
          dictOptions.value[index] = { ...dictOptions.value[index], ...optionForm.value }
        }
      } else {
        await createDictOption(currentDict.value.dict_code, optionForm.value)
        message.success('字典值创建成功')

        // 添加到本地列表
        dictOptions.value.push(optionForm.value)
      }

      optionDialogVisible.value = false
    } catch (error) {
      console.error('保存字典值失败:', error)
      message.error('保存字典值失败')
    } finally {
      optionSaving.value = false
    }
  })
}

// 确认删除字典值
const confirmDeleteOption = (option) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除选项 "${option.option_label}" 吗？删除后将无法恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => deleteSelectedOption(option)
  })
}

// 删除字典值
const deleteSelectedOption = async (option) => {
  if (!currentDict.value) return

  optionsLoading.value = true
  try {
    await deleteDictOption(currentDict.value.dict_code, option.option_value)
    message.success('字典值删除成功')

    // 从本地列表中移除
    dictOptions.value = dictOptions.value.filter(item => item.option_value !== option.option_value)
  } catch (error) {
    console.error('删除字典值失败:', error)
    message.error('删除字典值失败')
  } finally {
    optionsLoading.value = false
  }
}
</script>

<style scoped>
.dict-page {
  padding: 16px;
  height: 100%;
}

.dict-list-card,
.dict-values-card {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.search-box {
  margin-bottom: 16px;
}

.dict-list {
  flex: 1;
  overflow-y: auto;
}

.dict-values {
  flex: 1;
  overflow-y: auto;
}

.dict-code {
  color: #666;
  font-size: 12px;
}

.dict-remark {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

.active-dict {
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.n-list-item) {
  padding: 12px 16px;
}

:deep(.n-thing-main__description) {
  margin-top: 8px;
}

.dict-item-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.dict-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 12px;
  padding-top: 4px;
}

.action-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}
</style>
