/**
 * 订单表单数据工厂函数
 */
import { generateOrderNo, convertDateToTimestamp } from './orderFormUtils'
import { convertNumberToChinese } from './money'

/**
 * 创建默认的订单表单数据
 * @returns {Object} 默认表单数据
 */
export const createDefaultOrderForm = () => {
  return {
    id: null,
    batchCode: generateOrderNo(), // 自动生成订单编号
    dealDate: Date.now(), // 默认为当前日期
    invoiceOrgId: null, // 销售单位ID
    invoiceOrgName: '',
    paymentRemark: '',
    orderStatus: 'DEPOSIT', // 默认为大定金状态

    // 出库信息
    outboundOrgId: null, // 出库单位ID
    outboundOrgName: '', // 出库单位名称
    expectedOutboundDate: Date.now(), // 默认为当前日期

    // 客户信息
    customerId: null,
    customerName: '',
    customerPhone: '',
    customerAddress: '',
    salesperson: '',
    salesAgentId: null, // 销售顾问ID
    salesLeaderId: null, // 销售主管ID

    // 车辆信息
    skuId: null, // 车辆SKU ID
    vehicleBrand: '', // 车辆品牌
    vehicleSeries: '', // 车型
    vehicleConfig: '', // 配置
    vehicleColorCode: '', // 颜色代码
    vehicleSbPrice: 0, // 启票价格
    vehicleSalePrice: 0, // 销售价格

    // 金额信息
    depositAmount: 0, // 已付定金
    depositDeductible: true, // 定金是否转车款，默认为是
    discountAmount: 0, // 优惠金额
    exclusiveDiscountType: 'NONE', // 专享优惠类型，默认为"无"
    exclusiveDiscountAmount: 0, // 专享优惠金额
    salesExpense: 0, // 销售费用
    finalPrice: 0, // 成交总价
    finalPriceChinese: '零', // 成交总价（大写）
    profitRate: 0, // 预估毛利率
    profitAmount: 0, // 预估毛利润

    // 付款方式
    paymentMethod: 'FULL', // 默认全款，可选值：FULL（全款）、LOAN（贷款）

    // 贷款信息 - 付款方式为贷款时使用
    loanChannel: '', // 贷款渠道
    loanAmount: 0, // 贷款金额
    loanInitialAmount: 0, // 首付金额
    loanInitialRatio: 0, // 首付比例
    loanMonths: null, // 贷款期限（月）
    loanRebateAmount: 0, // 分期返利（客户）
    loanRebateDeductible: true, // 分期返利是否转车款，默认为是
    loanFee: 0, // 分期服务费，默认为0

    // 二手车置换信息
    hasUsedVehicle: 'NO', // 是否有车辆置换，默认为"无"，可选值：YES（有）、NO（无）
    usedVehicleId: '', // 二手车车牌号
    usedVehicleVin: '', // 二手车VIN
    usedVehicleAmount: 0, // 二手车置换金额
    usedVehicleDeductible: true, // 是否转车款，默认为是
    usedVehicleDiscountAmount: 0, // 二手车置换补贴
    usedVehicleDiscountDeductible: true, // 置换补贴是否转车款，默认为是
    usedVehicleBound: '', // 二手车品牌
    usedVehicleModel: '', // 二手车车型
    usedVehicleColor: '', // 二手车颜色

    // 车辆保险信息
    hasInsurance: 'NO', // 是否购买保险，默认为"无"，可选值：YES（有）、NO（无）

    // 其他衍生收入信息
    hasDerivativeIncome: 'NO', // 是否产生其他衍生收入，默认为"无"，可选值：YES（有）、NO（无）
    notaryFee: 0, // 公证费
    carefreeIncome: 0, // 畅行无忧收入
    extendedWarrantyIncome: 0, // 延保收入
    vpsIncome: 0, // VPS收入
    preInterest: 0, // 前置利息
    licensePlateFee: 0, // 挂牌费
    tempPlateFee: 0, // 临牌费
    deliveryEquipment: 0, // 外卖装具

    // 赠品明细信息
    hasGiftItems: 'NO', // 是否有赠品，默认为"无"，可选值：YES（有）、NO（无）
    giftItems: [] // 赠品明细数组
  }
}

/**
 * 合并初始数据到表单
 * @param {Object} form - 表单数据对象
 * @param {Object} initialData - 初始数据
 * @returns {Object} 合并后的表单数据
 */
export const mergeInitialData = (form, initialData) => {
  if (!initialData) return form

  const data = { ...initialData }

  // 确保日期是时间戳格式
  if (data.dealDate) {
    data.dealDate = convertDateToTimestamp(data.dealDate)
  }

  // 合并数据
  Object.assign(form, data)

  return form
}

/**
 * 将API数据转换为表单数据
 * @param {Object} apiData - API返回的数据
 * @param {Object} form - 表单数据对象
 * @returns {Object} 转换后的表单数据
 */
export const convertApiDataToForm = (apiData, form) => {
  if (!apiData) return form

  const formData = { ...form }
  const data = { ...apiData }

  // 设置金额字段默认值
  if (data.depositAmount !== undefined) {
    formData.depositAmount = data.depositAmount / 100
  }
  if (data.depositDeductible === undefined) formData.depositDeductible = true

  if (data.discountAmount !== undefined) {
    formData.discountAmount = data.discountAmount / 100
  }
  if (data.exclusiveDiscountType !== undefined) {
    formData.exclusiveDiscountType = data.exclusiveDiscountType
  } else {
    formData.exclusiveDiscountType = 'NONE'
  }
  if (data.exclusiveDiscountAmount !== undefined) {
    formData.exclusiveDiscountAmount = data.exclusiveDiscountAmount / 100
  }
  if (data.salesCostAmount !== undefined) {
    formData.salesExpense = data.salesCostAmount / 100
  }
  if (data.dealAmount !== undefined) {
    formData.finalPrice = data.dealAmount / 100
  }
  if (data.dealAmountCn !== undefined) {
    formData.finalPriceChinese = data.dealAmountCn
  } else {
    formData.finalPriceChinese = convertNumberToChinese(formData.finalPrice || 0)
  }
  if (data.grossProfitAmount !== undefined) {
    formData.profitAmount = data.grossProfitAmount / 100
  }
  if (data.grossProfitRate !== undefined) {
    formData.profitRate = data.grossProfitRate
  }

  // 处理付款方式
  if (data.paymentMethod !== undefined) {
    formData.paymentMethod = data.paymentMethod
  }

  // 处理贷款信息
  if (data.loanChannel !== undefined) {
    formData.loanChannel = data.loanChannel
  }
  if (data.loanAmount !== undefined) {
    formData.loanAmount = data.loanAmount / 100
  }
  if (data.loanInitialAmount !== undefined) {
    formData.loanInitialAmount = data.loanInitialAmount / 100
  }
  if (data.loanInitialRatio !== undefined) {
    formData.loanInitialRatio = data.loanInitialRatio
  }
  if (data.loanMonths !== undefined) {
    formData.loanMonths = data.loanMonths
  }
  if (data.loanFee !== undefined) {
    formData.loanFee = data.loanFee / 100
  }

  // 处理二手车信息
  if (data.usedVehicleId !== undefined) {
    formData.usedVehicleId = data.usedVehicleId
    // 如果有二手车ID，则设置hasUsedVehicle为"有"
    formData.hasUsedVehicle = 'YES'
  } else {
    // 如果没有二手车ID，则设置hasUsedVehicle为"无"
    formData.hasUsedVehicle = 'NO'
  }

  if (data.usedVehicleVin !== undefined) {
    formData.usedVehicleVin = data.usedVehicleVin
  }
  if (data.usedVehicleAmount !== undefined) {
    formData.usedVehicleAmount = data.usedVehicleAmount / 100
  }
  if (data.usedVehicleDiscountAmount !== undefined) {
    formData.usedVehicleDiscountAmount = data.usedVehicleDiscountAmount / 100
  }

  // 处理其他衍生收入信息
  if (data.hasDerivativeIncome !== undefined) {
    formData.hasDerivativeIncome = data.hasDerivativeIncome
  }

  // 处理赠品明细信息
  if (data.hasGiftItems !== undefined) {
    formData.hasGiftItems = data.hasGiftItems
  }
  if (data.giftItems !== undefined && Array.isArray(data.giftItems)) {
    formData.giftItems = data.giftItems
  } else {
    formData.giftItems = []
  }

  return formData
}

export default {
  createDefaultOrderForm,
  mergeInitialData,
  convertApiDataToForm
}
