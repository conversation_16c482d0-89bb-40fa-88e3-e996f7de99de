import { doGet, doPost, doPut, doDelete } from '@/utils/requests'
import { dictList, dictOptions } from '@/mock/dictData'

// 判断是否为开发环境
const isDev = true //import.meta.env.MODE === 'production'

/**
 * 获取字典列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回字典列表的Promise
 */
export function getDictList(params) {
  if (isDev) {
    // 开发环境使用模拟数据
    return Promise.resolve({
      code: 200,
      data: dictList,
      message: 'success'
    })
  }
  return doGet('/system/dict', params)
}

/**
 * 获取字典详情
 * @param {String} dictCode - 字典编码
 * @returns {Promise} 返回字典详情的Promise
 */
export function getDictDetail(dictCode) {
  if (isDev) {
    // 开发环境使用模拟数据
    const dict = dictList.find(item => item.dict_code === dictCode)
    return Promise.resolve({
      code: 200,
      data: dict || null,
      message: dict ? 'success' : 'not found'
    })
  }
  return doGet(`/system/dict/${dictCode}`)
}

/**
 * 获取字典选项列表
 * @param {String} dictCode - 字典编码
 * @returns {Promise} 返回字典选项列表的Promise
 */
export function getDictOptions(dictCode) {
  if (isDev) {
    // 开发环境使用模拟数据
    const options = dictOptions[dictCode] || []
    return Promise.resolve({
      code: 200,
      data: options,
      message: 'success'
    })
  }
  return doGet(`/system/dict/${dictCode}/options`)
}

/**
 * 创建字典
 * @param {Object} dictData - 字典数据
 * @returns {Promise} 返回创建结果的Promise
 */
export function createDict(dictData) {
  if (isDev) {
    // 开发环境使用模拟数据
    // 检查是否已存在相同编码的字典
    const exists = dictList.some(item => item.dict_code === dictData.dict_code)
    if (exists) {
      return Promise.reject({
        code: 400,
        message: '字典编码已存在'
      })
    }

    // 添加到模拟数据中
    dictList.push(dictData)
    dictOptions[dictData.dict_code] = []

    return Promise.resolve({
      code: 200,
      data: dictData,
      message: 'success'
    })
  }
  return doPost('/system/dict', dictData)
}

/**
 * 更新字典
 * @param {Object} dictData - 字典数据
 * @returns {Promise} 返回更新结果的Promise
 */
export function updateDict(dictData) {
  if (isDev) {
    // 开发环境使用模拟数据
    const index = dictList.findIndex(item => item.dict_code === dictData.dict_code)
    if (index === -1) {
      return Promise.reject({
        code: 404,
        message: '字典不存在'
      })
    }

    // 更新模拟数据
    dictList[index] = { ...dictList[index], ...dictData }

    return Promise.resolve({
      code: 200,
      data: dictList[index],
      message: 'success'
    })
  }
  return doPut(`/system/dict/${dictData.dict_code}`, dictData)
}

/**
 * 删除字典
 * @param {String} dictCode - 字典编码
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteDict(dictCode) {
  if (isDev) {
    // 开发环境使用模拟数据
    const index = dictList.findIndex(item => item.dict_code === dictCode)
    if (index === -1) {
      return Promise.reject({
        code: 404,
        message: '字典不存在'
      })
    }

    // 从模拟数据中删除
    dictList.splice(index, 1)
    delete dictOptions[dictCode]

    return Promise.resolve({
      code: 200,
      message: 'success'
    })
  }
  return doDelete(`/system/dict/${dictCode}`)
}

/**
 * 创建字典选项
 * @param {String} dictCode - 字典编码
 * @param {Object} optionData - 选项数据
 * @returns {Promise} 返回创建结果的Promise
 */
export function createDictOption(dictCode, optionData) {
  if (isDev) {
    // 开发环境使用模拟数据
    // 检查字典是否存在
    const dictIndex = dictList.findIndex(item => item.dict_code === dictCode)
    if (dictIndex === -1) {
      return Promise.reject({
        code: 404,
        message: '字典不存在'
      })
    }

    // 检查选项是否已存在
    if (!dictOptions[dictCode]) {
      dictOptions[dictCode] = []
    }

    const exists = dictOptions[dictCode].some(item => item.option_value === optionData.option_value)
    if (exists) {
      return Promise.reject({
        code: 400,
        message: '选项值已存在'
      })
    }

    // 添加到模拟数据中
    dictOptions[dictCode].push(optionData)

    return Promise.resolve({
      code: 200,
      data: optionData,
      message: 'success'
    })
  }
  return doPost(`/system/dict/${dictCode}/option`, optionData)
}

/**
 * 更新字典选项
 * @param {String} dictCode - 字典编码
 * @param {Object} optionData - 选项数据
 * @returns {Promise} 返回更新结果的Promise
 */
export function updateDictOption(dictCode, optionData) {
  if (isDev) {
    // 开发环境使用模拟数据
    // 检查字典是否存在
    const dictIndex = dictList.findIndex(item => item.dict_code === dictCode)
    if (dictIndex === -1) {
      return Promise.reject({
        code: 404,
        message: '字典不存在'
      })
    }

    // 检查选项是否存在
    if (!dictOptions[dictCode]) {
      return Promise.reject({
        code: 404,
        message: '字典选项不存在'
      })
    }

    const optionIndex = dictOptions[dictCode].findIndex(item => item.option_value === optionData.option_value)
    if (optionIndex === -1) {
      return Promise.reject({
        code: 404,
        message: '字典选项不存在'
      })
    }

    // 更新模拟数据
    dictOptions[dictCode][optionIndex] = { ...dictOptions[dictCode][optionIndex], ...optionData }

    return Promise.resolve({
      code: 200,
      data: dictOptions[dictCode][optionIndex],
      message: 'success'
    })
  }
  return doPut(`/system/dict/${dictCode}/option/${optionData.option_value}`, optionData)
}

/**
 * 删除字典选项
 * @param {String} dictCode - 字典编码
 * @param {String} optionValue - 选项值
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteDictOption(dictCode, optionValue) {
  if (isDev) {
    // 开发环境使用模拟数据
    // 检查字典是否存在
    const dictIndex = dictList.findIndex(item => item.dict_code === dictCode)
    if (dictIndex === -1) {
      return Promise.reject({
        code: 404,
        message: '字典不存在'
      })
    }

    // 检查选项是否存在
    if (!dictOptions[dictCode]) {
      return Promise.reject({
        code: 404,
        message: '字典选项不存在'
      })
    }

    const optionIndex = dictOptions[dictCode].findIndex(item => item.option_value === optionValue)
    if (optionIndex === -1) {
      return Promise.reject({
        code: 404,
        message: '字典选项不存在'
      })
    }

    // 从模拟数据中删除
    dictOptions[dictCode].splice(optionIndex, 1)

    return Promise.resolve({
      code: 200,
      message: 'success'
    })
  }
  return doDelete(`/system/dict/${dictCode}/option/${optionValue}`)
}
