.biz-org-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden; /* 防止页面整体滚动 */
}

.filter-card {
  margin-bottom: 0;
  position: relative;
}

.toolbar {
  margin-bottom: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
}

/* 市/区选项的过渡效果 */
.city-filter-enter-active,
.city-filter-leave-active {
  transition: all 0.3s ease;
}

.city-filter-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.city-filter-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.city-filter-enter-to,
.city-filter-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

/* 可展开筛选条件样式 */
.expandable-filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 展开/收起按钮样式 */
.filter-toggle {
  position: absolute;
  bottom: -20px; /* 让边框从按钮中央穿过 */
  left: 95%;
  transform: translateX(-50%); /* 水平居中 */
  z-index: 10;
}

.toggle-button {
  background-color: #fff !important; /* 强制不透明白色背景 */
  padding: 6px 16px;
  font-size: 13px;
  transition: all 0.3s ease;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e6;
  position: relative;
}

/* 确保按钮内容不透明 */
:deep(.toggle-button .n-button__content) {
  background-color: #fff !important;
  opacity: 1 !important;
}

/* 确保按钮本身不透明 */
:deep(.toggle-button.n-button--text-type) {
  background-color: #fff !important;
  opacity: 1 !important;
}

.toggle-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
  background-color: #fff !important;
}

/* 确保按钮在卡片边框上的视觉效果 */
:deep(.filter-card .n-card) {
  overflow: visible;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-separator {
  color: #999;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.department-selector) {
  width: 350px !important;
  min-width: 350px !important;
}

/* 自定义单选按钮组样式 */
:deep(.custom-radio-group .custom-radio-button) {
  border: none;
  background: transparent;
  transition: all 0.2s;
}

:deep(.custom-radio-group .custom-radio-button:hover) {
  color: var(--primary-color);
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.custom-radio-group .custom-radio-button--checked) {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border: none !important;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 0; /* 确保flex子项可以收缩 */
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fff;
  overflow: hidden;
}

.data-table {
  flex: 1;
  overflow: hidden;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  flex-shrink: 0; /* 防止分页区域被压缩 */
}

/* 表格滚动样式 */
:deep(.data-table .n-data-table) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.data-table .n-data-table-wrapper) {
  flex: 1;
  overflow: auto;
}

:deep(.data-table .n-data-table-base-table-body) {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* 确保表格头部固定 */
:deep(.data-table .n-data-table-thead) {
  position: sticky;
  top: 0;
  z-index: 5;
  background-color: #fafafa;
}

/* 表格基础样式 */
:deep(.n-data-table) {
  position: relative;
}

:deep(.n-data-table-wrapper) {
  overflow: auto;
  position: relative;
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}
