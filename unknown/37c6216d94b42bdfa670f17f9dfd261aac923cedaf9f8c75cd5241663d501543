<template>
    <div class="test-page">
        <n-card title="部门选择器移动端测试">
            <n-space vertical :size="24">
                <!-- 单选模式测试 -->
                <div class="test-section">
                    <div class="test-title">单选模式</div>
                    <department-selector
                        v-model="singleDepartment"
                        mode="single"
                        label="选择部门"
                    />
                    <div class="test-result">
                        已选择：{{ singleDepartment?.name || '未选择' }}
                    </div>
                </div>

                <!-- 多选模式测试 -->
                <div class="test-section">
                    <div class="test-title">多选模式</div>
                    <department-selector
                        v-model="multipleDepartments"
                        mode="multiple"
                        label="选择多个部门"
                    />
                    <div class="test-result">
                        已选择：{{ multipleDepartments.length ? multipleDepartments.map(d => d.name).join('、') : '未选择' }}
                    </div>
                </div>

                <!-- 禁用状态测试 -->
                <div class="test-section">
                    <div class="test-title">禁用状态</div>
                    <department-selector
                        v-model="singleDepartment"
                        mode="single"
                        label="选择部门"
                        disabled
                    />
                </div>

                <!-- 自定义宽度测试 -->
                <div class="test-section">
                    <div class="test-title">自定义宽度</div>
                    <department-selector
                        v-model="singleDepartment"
                        mode="single"
                        label="选择部门"
                        width="350px"
                    />
                </div>
            </n-space>
        </n-card>

        <!-- 移动端提示 -->
        <div class="mobile-tip" v-if="!isMobile">
            请使用移动设备或浏览器开发工具的移动端模式查看
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { NCard, NSpace } from 'naive-ui'
import DepartmentSelector from '@/components/users/DepartmentSelector.vue'

// 测试数据
const singleDepartment = ref(null)
const multipleDepartments = ref([])

// 移动端检测
const isMobile = computed(() => {
    return window.innerWidth <= 768
})
</script>

<style scoped>
.test-page {
    padding: 16px;
    max-width: 100%;
    box-sizing: border-box;
}

.test-section {
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.test-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
}

.test-result {
    margin-top: 12px;
    padding: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
}

.mobile-tip {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1000;
}

/* 移动端样式适配 */
@media screen and (max-width: 768px) {
    .test-page {
        padding: 12px;
    }

    .test-section {
        padding: 12px;
    }

    :deep(.n-card-header) {
        padding: 12px 16px;
    }

    :deep(.n-card-content) {
        padding: 0 16px 16px;
    }

    .test-title {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .test-result {
        margin-top: 8px;
        padding: 6px;
        font-size: 13px;
    }
}
</style> 