<template>
  <div class="test-page">
    <n-card title="QueryPage 功能测试">
      <n-space vertical>
        <n-alert type="info" title="测试说明">
          这个页面用于测试 QueryPage 组件的各种功能，包括新增的字段类型和冻结列功能。
        </n-alert>
        
        <n-space>
          <n-button type="primary" @click="goToEnhancedExample">
            查看增强功能演示
          </n-button>
          <n-button @click="goToSimpleExample">
            查看简单示例
          </n-button>
          <n-button @click="goToRefactoredExample">
            查看重构示例
          </n-button>
        </n-space>

        <n-divider />

        <n-descriptions title="新增功能列表" bordered :column="2">
          <n-descriptions-item label="数字字段">
            支持小数位数配置，千分位格式化显示
          </n-descriptions-item>
          <n-descriptions-item label="货币字段">
            带货币符号，绿色加粗显示，支持多种货币
          </n-descriptions-item>
          <n-descriptions-item label="字典字段">
            支持dictKey从API加载，n-tag形式渲染
          </n-descriptions-item>
          <n-descriptions-item label="冻结列">
            横向滚动+冻结首列，支持多个冻结列
          </n-descriptions-item>
          <n-descriptions-item label="多值字段">
            支持逗号分隔的多值显示为多个tag
          </n-descriptions-item>
          <n-descriptions-item label="字典缓存">
            自动缓存字典数据，避免重复API调用
          </n-descriptions-item>
        </n-descriptions>

        <n-divider />

        <n-card title="路由测试">
          <n-space vertical>
            <n-text>当前路由: {{ $route.path }}</n-text>
            <n-text>页面标题: {{ $route.meta.title }}</n-text>
            <n-text>是否需要认证: {{ $route.meta.requiresAuth ? '是' : '否' }}</n-text>
          </n-space>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { 
  NCard, NSpace, NAlert, NButton, NDivider, 
  NDescriptions, NDescriptionsItem, NText 
} from 'naive-ui'

const router = useRouter()

const goToEnhancedExample = () => {
  router.push('/examples/enhanced-query-page')
}

const goToSimpleExample = () => {
  // 这个路由可能需要添加
  console.log('简单示例路由待添加')
}

const goToRefactoredExample = () => {
  // 这个路由可能需要添加
  console.log('重构示例路由待添加')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}
</style>
