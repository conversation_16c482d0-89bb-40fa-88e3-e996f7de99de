<template>
  <div>
    <div class="section-title">
      <span class="title-text">赠品明细</span>
    </div>
    <n-divider title-placement="left"></n-divider>

    <div class="option-row">
      <span class="option-label">本单是否存在赠送商品或服务？</span>
      <n-radio-group v-model:value="form.hasGiftItems">
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span class="option-tip" v-if="form.hasGiftItems === 'YES'">
        点击➕图标选择赠送商品或服务，赠品成本将计入本单销售费用。
      </span>
    </div>

    <!-- 当选择"是"时显示赠品明细表格 -->
    <n-grid v-if="form.hasGiftItems === 'YES'" :cols="4" :x-gap="16" :y-gap="1">
      <n-grid-item span="3">
        <gift-items-table v-model="form.giftItems" ref="giftItemsTableRef" />
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import GiftItemsTable from '@/components/orders/GiftItemsTable.vue'

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})

// 表格引用
const giftItemsTableRef = ref(null)

// 暴露方法给父组件
defineExpose({
  giftItemsTableRef
})
</script>
